'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { THEME_MODES, STORAGE_KEY, lightTheme, darkTheme, tealTheme } from './config';

// Create the theme context
const ThemeContext = createContext(undefined);

/**
 * Custom hook to access theme context
 * @returns {Object} Theme context value
 * @throws {Error} If used outside of ThemeProvider
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * Get the system's preferred color scheme
 * @returns {string} 'light' or 'dark'
 */
const getSystemTheme = () => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches 
      ? THEME_MODES.DARK 
      : THEME_MODES.LIGHT;
  }
  return THEME_MODES.LIGHT;
};

/**
 * Get the stored theme preference from localStorage
 * @returns {string} Stored theme mode or system default
 */
const getStoredTheme = () => {
  if (typeof window !== 'undefined') {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored && Object.values(THEME_MODES).includes(stored)) {
        return stored;
      }
    } catch (error) {
      console.warn('Failed to read theme from localStorage:', error);
    }
  }
  return THEME_MODES.SYSTEM;
};

/**
 * Store theme preference in localStorage
 * @param {string} theme - Theme mode to store
 */
const storeTheme = (theme) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(STORAGE_KEY, theme);
    } catch (error) {
      console.warn('Failed to store theme in localStorage:', error);
    }
  }
};

/**
 * Resolve the actual theme based on mode and system preference
 * @param {string} mode - Theme mode (light, dark, teal, system)
 * @returns {string} Resolved theme ('light', 'dark', or 'teal')
 */
const resolveTheme = (mode) => {
  if (mode === THEME_MODES.SYSTEM) {
    return getSystemTheme();
  }
  return mode;
};

/**
 * Get theme tokens based on resolved theme
 * @param {string} resolvedTheme - 'light', 'dark', or 'teal'
 * @returns {Object} Theme tokens
 */
const getThemeTokens = (resolvedTheme) => {
  if (resolvedTheme === THEME_MODES.DARK) return darkTheme;
  if (resolvedTheme === THEME_MODES.TEAL) return tealTheme;
  return lightTheme;
};

/**
 * Apply theme to document root
 * @param {string} resolvedTheme - 'light' or 'dark'
 * @param {Object} tokens - Theme tokens
 */
const applyThemeToDocument = (resolvedTheme, tokens) => {
  if (typeof document !== 'undefined') {
    const root = document.documentElement;
    
    // Set theme mode attribute
    root.setAttribute('data-theme', resolvedTheme);
    
    // Apply CSS custom properties
    Object.entries(tokens).forEach(([category, values]) => {
      if (typeof values === 'object' && values !== null) {
        Object.entries(values).forEach(([key, value]) => {
          root.style.setProperty(`--theme-${category}-${key}`, value);
        });
      }
    });
  }
};

/**
 * Theme Provider Component
 * Manages theme state and provides theme context to children
 */
export const ThemeProvider = ({ children, defaultTheme = THEME_MODES.SYSTEM }) => {
  const [mode, setMode] = useState(defaultTheme);
  const [resolvedTheme, setResolvedTheme] = useState(THEME_MODES.LIGHT);
  const [isHydrated, setIsHydrated] = useState(false);

  // Initialize theme on client side
  useEffect(() => {
    const storedTheme = getStoredTheme();
    const resolved = resolveTheme(storedTheme);
    
    setMode(storedTheme);
    setResolvedTheme(resolved);
    setIsHydrated(true);
    
    // Apply theme immediately
    const tokens = getThemeTokens(resolved);
    applyThemeToDocument(resolved, tokens);
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    if (!isHydrated) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = (e) => {
      if (mode === THEME_MODES.SYSTEM) {
        const newResolvedTheme = e.matches ? THEME_MODES.DARK : THEME_MODES.LIGHT;
        setResolvedTheme(newResolvedTheme);
        
        const tokens = getThemeTokens(newResolvedTheme);
        applyThemeToDocument(newResolvedTheme, tokens);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, [mode, isHydrated]);

  // Update theme when mode changes
  useEffect(() => {
    if (!isHydrated) return;

    const resolved = resolveTheme(mode);
    setResolvedTheme(resolved);
    
    const tokens = getThemeTokens(resolved);
    applyThemeToDocument(resolved, tokens);
    
    storeTheme(mode);
  }, [mode, isHydrated]);

  /**
   * Set theme mode
   * @param {string} newMode - New theme mode
   */
  const setTheme = (newMode) => {
    console.log('setTheme called with:', newMode);
    console.log('Available modes:', Object.values(THEME_MODES));
    console.log('Is valid mode:', Object.values(THEME_MODES).includes(newMode));

    if (Object.values(THEME_MODES).includes(newMode)) {
      console.log('Setting mode to:', newMode);
      setMode(newMode);
    } else {
      console.warn(`Invalid theme mode: ${newMode}`);
    }
  };

  /**
   * Toggle between light and dark themes
   */
  const toggleTheme = () => {
    const newMode = resolvedTheme === THEME_MODES.DARK 
      ? THEME_MODES.LIGHT 
      : THEME_MODES.DARK;
    setTheme(newMode);
  };

  /**
   * Check if current theme is dark
   * @returns {boolean}
   */
  const isDark = resolvedTheme === THEME_MODES.DARK;

  /**
   * Check if current theme is light
   * @returns {boolean}
   */
  const isLight = resolvedTheme === THEME_MODES.LIGHT;

  /**
   * Check if system theme preference is being used
   * @returns {boolean}
   */
  const isSystemMode = mode === THEME_MODES.SYSTEM;

  // Get current theme tokens
  const tokens = getThemeTokens(resolvedTheme);

  const contextValue = {
    // Current state
    mode,
    resolvedTheme,
    tokens,
    isHydrated,
    
    // Theme checks
    isDark,
    isLight,
    isSystemMode,
    
    // Theme actions
    setTheme,
    toggleTheme,
    
    // Available modes
    modes: THEME_MODES
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
