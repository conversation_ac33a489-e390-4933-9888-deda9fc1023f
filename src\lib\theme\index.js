/**
 * Theme System - Main Export File
 * Provides easy access to all theme-related functionality
 */

// Core theme functionality
export { ThemeProvider, useTheme as useThemeContext } from './ThemeContext';
export { useTheme, useThemeMediaQuery, useThemeAnimation } from './useTheme';

// Theme configuration
export {
  THEME_MODES,
  STORAGE_KEY,
  colors,
  lightTheme,
  darkTheme,
  typography,
  spacing,
  borderRadius,
  animation
} from './config';

// Re-export default config
export { default as themeConfig } from './config';

/**
 * Utility function to create theme-aware class names
 * @param {string} baseClasses - Base CSS classes
 * @param {Object} themeClasses - Theme-specific classes
 * @param {boolean} isDark - Whether current theme is dark
 * @returns {string} Combined class names
 */
export const createThemeClasses = (baseClasses = '', themeClasses = {}, isDark = false) => {
  const themeSpecific = isDark ? themeClasses.dark : themeClasses.light;
  return [baseClasses, themeSpecific].filter(Boolean).join(' ');
};

/**
 * Get theme token value by path
 * @param {Object} tokens - Theme tokens object
 * @param {string} path - Dot notation path to token
 * @returns {string|undefined} Token value
 */
export const getThemeToken = (tokens, path) => {
  const keys = path.split('.');
  let value = tokens;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return undefined;
    }
  }

  return value;
};

/**
 * Create CSS custom property name from token path
 * @param {string} path - Token path
 * @returns {string} CSS custom property name
 */
export const createCSSVar = (path) => {
  return `--theme-${path.replace(/\./g, '-')}`;
};

/**
 * Create CSS var() function call
 * @param {string} path - Token path
 * @param {string} fallback - Fallback value
 * @returns {string} CSS var() function
 */
export const cssVar = (path, fallback = '') => {
  const varName = createCSSVar(path);
  return fallback ? `var(${varName}, ${fallback})` : `var(${varName})`;
};
