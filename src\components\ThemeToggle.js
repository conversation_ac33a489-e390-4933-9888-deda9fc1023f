'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '../lib/theme/useTheme';

/**
 * Simple Theme Toggle Button
 * Toggles between light and dark themes
 */
export const ThemeToggle = ({ 
  className = '',
  size = 'md',
  showLabel = false,
  variant = 'default'
}) => {
  const { isDark, toggleTheme, isHydrated } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted || !isHydrated) {
    return (
      <button 
        className={`theme-toggle-skeleton ${getSizeClasses(size)} ${className}`}
        disabled
        aria-label="Loading theme toggle"
      >
        <div className="animate-pulse bg-gray-300 rounded w-5 h-5" />
      </button>
    );
  }

  const sizeClasses = getSizeClasses(size);
  const variantClasses = getVariantClasses(variant);

  return (
    <button
      onClick={toggleTheme}
      className={`theme-toggle ${sizeClasses} ${variantClasses} ${className}`}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
      title={`Switch to ${isDark ? 'light' : 'dark'} theme`}
    >
      <span className="theme-toggle-icon">
        {isDark ? <SunIcon /> : <MoonIcon />}
      </span>
      {showLabel && (
        <span className="theme-toggle-label">
          {isDark ? 'Light' : 'Dark'}
        </span>
      )}
    </button>
  );
};

/**
 * Advanced Theme Selector
 * Allows selection between light, dark, and system themes
 */
export const ThemeSelector = ({ 
  className = '',
  variant = 'dropdown',
  showIcons = true 
}) => {
  const { mode, setTheme, modes, isHydrated } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || !isHydrated) {
    return (
      <div className={`theme-selector-skeleton ${className}`}>
        <div className="animate-pulse bg-gray-300 rounded w-24 h-8" />
      </div>
    );
  }

  const options = [
    { value: modes.LIGHT, label: 'Light', icon: <SunIcon /> },
    { value: modes.DARK, label: 'Dark', icon: <MoonIcon /> },
    { value: modes.SYSTEM, label: 'System', icon: <SystemIcon /> }
  ];

  if (variant === 'buttons') {
    return (
      <div className={`theme-selector-buttons ${className}`}>
        {options.map((option) => (
          <button
            key={option.value}
            onClick={() => setTheme(option.value)}
            className={`theme-option-button ${mode === option.value ? 'active' : ''}`}
            aria-label={`Set ${option.label} theme`}
          >
            {showIcons && <span className="theme-option-icon">{option.icon}</span>}
            <span className="theme-option-label">{option.label}</span>
          </button>
        ))}
      </div>
    );
  }

  return (
    <div className={`theme-selector-dropdown ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="theme-selector-trigger"
        aria-label="Select theme"
        aria-expanded={isOpen}
      >
        {showIcons && (
          <span className="theme-selector-current-icon">
            {options.find(opt => opt.value === mode)?.icon}
          </span>
        )}
        <span className="theme-selector-current-label">
          {options.find(opt => opt.value === mode)?.label}
        </span>
        <ChevronIcon className={`theme-selector-chevron ${isOpen ? 'open' : ''}`} />
      </button>
      
      {isOpen && (
        <div className="theme-selector-menu">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => {
                setTheme(option.value);
                setIsOpen(false);
              }}
              className={`theme-selector-option ${mode === option.value ? 'active' : ''}`}
            >
              {showIcons && <span className="theme-option-icon">{option.icon}</span>}
              <span className="theme-option-label">{option.label}</span>
              {mode === option.value && <CheckIcon className="theme-option-check" />}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

// Helper functions
const getSizeClasses = (size) => {
  const sizes = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  };
  return sizes[size] || sizes.md;
};

const getVariantClasses = (variant) => {
  const variants = {
    default: 'bg-theme-bg-secondary hover:bg-theme-bg-tertiary border border-theme-border-primary',
    ghost: 'hover:bg-theme-bg-secondary',
    outline: 'border-2 border-theme-border-primary hover:bg-theme-bg-secondary'
  };
  return variants[variant] || variants.default;
};

// Icon components
const SunIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <circle cx="12" cy="12" r="5" />
    <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
  </svg>
);

const MoonIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
  </svg>
);

const SystemIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
    <line x1="8" y1="21" x2="16" y2="21" />
    <line x1="12" y1="17" x2="12" y2="21" />
  </svg>
);

const ChevronIcon = ({ className = '' }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <polyline points="6,9 12,15 18,9" />
  </svg>
);

const CheckIcon = ({ className = '' }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <polyline points="20,6 9,17 4,12" />
  </svg>
);

export default ThemeToggle;
