'use client';

import React, { useState, useEffect , useRef } from 'react';
import Image from 'next/image';
import <PERSON><PERSON> from 'lottie-react';
import { useTheme } from '../lib/theme/useTheme';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [currentTheme, setCurrentTheme] = useState('light');
  const [showCompanySubmenu, setShowCompanySubmenu] = useState(false);
  const [showServicesSubmenu, setShowServicesSubmenu] = useState(false);
  const [showTechnologiesSubmenu, setShowTechnologiesSubmenu] = useState(false);
  const [showProductsSubmenu, setShowProductsSubmenu] = useState(false);
  const [showHireSubmenu, setShowHireSubmenu] = useState(false);
  const { getToken, mode, modes, isDark, isLight, resolvedTheme, isHydrated } = useTheme();

  // Cache for loaded animations to prevent re-loading
  const animationCache = useRef(new Map());

  // Preload commonly used icons on component mount
  useEffect(() => {
    const preloadIcons = async () => {
      const commonIcons = [
        '/icons/company.png',
        '/icons/work-life-balance.png',
        '/icons/trophy.png',
        '/icons/enterprice.png',
        '/icons/hiring.png',
        '/icons/blogging.png',
        '/icons/referal.png',
        '/icons/mobile-development.png',
        '/icons/android_development.png',
        '/icons/ios_development.png',
        '/icons/cross_platform_development.png',
        '/icons/startup.png',

        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/game-development.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
        '/icons/startup.png',
    
      ];

      // Preload images
      commonIcons.forEach(src => {
        if (src.endsWith('.gif') || src.endsWith('.png') || src.endsWith('.jpg')) {
          const img = new Image();
          img.src = src;
        }
      });
    };

    preloadIcons();
  }, []);

  // Helper component to render Lottie animation with caching
  const LottieIcon = ({ src, className }) => {
    const [animationData, setAnimationData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    useEffect(() => {
      const loadAnimation = async () => {
        try {
          setIsLoading(true);
          setHasError(false);

          // Check cache first
          if (animationCache.current.has(src)) {
            setAnimationData(animationCache.current.get(src));
            setIsLoading(false);
            return;
          }

          const response = await fetch(src);
          if (!response.ok) {
            throw new Error(`Failed to fetch: ${response.status}`);
          }
          const data = await response.json();

          // Cache the animation data
          animationCache.current.set(src, data);
          setAnimationData(data);
        } catch (error) {
          console.error('Failed to load Lottie animation:', error);
          setHasError(true);
        } finally {
          setIsLoading(false);
        }
      };

      if (src.endsWith('.json')) {
        loadAnimation();
      }
    }, [src]);

    if (isLoading) {
      // Show theme-appropriate loading placeholder with better styling
      return (
        <div className={`${className} bg-white/10 animate-pulse rounded-lg flex items-center justify-center`}>
          <div className="w-4 h-4 bg-white/30 rounded-full animate-bounce"></div>
        </div>
      );
    }

    if (hasError || !animationData) {
      // Show a white icon placeholder for failed Lottie animations
      return (
        <div className={`${className} bg-white/20 rounded-lg flex items-center justify-center`}>
          <svg className="w-5 h-5 text-white opacity-80" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    return (
      <div className={className}>
        <Lottie
          animationData={animationData}
          loop={true}
          autoplay={true}
          style={{ width: '100%', height: '100%' }}
          rendererSettings={{
            preserveAspectRatio: 'xMidYMid slice'
          }}
        />
      </div>
    );
  };

  // Helper function to get icon filter based on context
  const getIconFilter = (isSubmenu = false) => {
    if (isSubmenu) {
      // Submenu always has dark background with white text, so always use white icons
      return 'filter brightness-0 invert opacity-80 group-hover:opacity-100'; // White icons for submenu
    } else {
      // For navbar icons, use theme-based coloring
      if (currentTheme === 'dark' || currentTheme === 'teal') {
        return 'filter brightness-0 invert opacity-80 group-hover:opacity-100'; // White icons
      } else {
        return 'filter brightness-0 opacity-80 group-hover:opacity-100'; // Black icons for light theme
      }
    }
  };

  // Helper function to get SVG icon for specific items
  const getSVGIcon = (iconName) => {
    switch (iconName) {
      case 'hiring':
        return (
          <svg className="w-8 h-8 text-white opacity-80 group-hover:opacity-100" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Helper function to render icon (Image, Lottie, or SVG)
  const renderIcon = (iconSrc, altText, width = 32, height = 32, className = "", isSubmenu = false) => {
    // Check if it's a predefined SVG icon
    const svgIcon = getSVGIcon(iconSrc);
    if (svgIcon) {
      return svgIcon;
    }

    if (iconSrc.endsWith('.json')) {
      // For Lottie animations - they handle their own colors
      return <LottieIcon src={iconSrc} className={className} />;
    } else {
      // For regular images (PNG, GIF, SVG)
      const isGif = iconSrc.includes('.gif');
      if (isGif) {
        // GIFs keep their original colors
        return (
          <Image
            src={iconSrc}
            alt={altText}
            width={width}
            height={height}
            className={`${className} opacity-80 group-hover:opacity-100`}
          />
        );
      } else {
        // PNG/SVG icons get theme-based coloring
        return (
          <Image
            src={iconSrc}
            alt={altText}
            width={width}
            height={height}
            className={`${className} ${getIconFilter(isSubmenu)}`}
          />
        );
      }
    }
  };

  const getNavbarClass = () => {
    // Use current theme state for instant updates
    if (currentTheme === 'dark') {
      return 'navbar-dark-bg';
    }
    if (currentTheme === 'teal') {
      return 'navbar-teal-bg';
    }
    return 'navbar-green-bg'; // default for light
  };

  const getTextColor = () => {
    if (currentTheme === 'dark') return 'text-gray-100';
    if (currentTheme === 'teal') return 'text-teal-50';
    return 'text-white'; // default for light/green
  };

  const getHoverColor = () => {
    if (currentTheme === 'dark') return 'hover:text-gray-200 hover:bg-gray-700/20';
    if (currentTheme === 'teal') return 'hover:text-teal-100 hover:bg-teal-600/20';
    return 'hover:text-green-100 hover:bg-white/10'; // default for light/green
  };

  useEffect(() => {
    // Initialize theme on mount
    const theme = localStorage.getItem('brtsoft-theme') || 'light';
    setCurrentTheme(theme);

    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    const handleThemeChange = () => {
      // Get current theme from localStorage and update state
      const theme = localStorage.getItem('brtsoft-theme') || 'light';
      console.log('Navbar theme changed to:', theme);
      setCurrentTheme(theme);
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('themechange', handleThemeChange);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('themechange', handleThemeChange);
    };
  }, []);

  const navItems = [
    { name: 'COMPANY', href: '#company', icon: '/icons/company.png', hasSubmenu: true, submenuType: 'company' },
    { name: 'SERVICES', href: '#services', icon: '/icons/services.png', hasSubmenu: true, submenuType: 'services' },
    { name: 'TECHNOLOGIES', href: '#technologies', icon: '/icons/technology.png', hasSubmenu: true, submenuType: 'technologies' },
    { name: 'PRODUCTS', href: '#products', icon: '/icons/product.png', hasSubmenu: true, submenuType: 'products' },
    { name: 'HIRE', href: '#hire', icon: '/icons/hire.png', hasSubmenu: true, submenuType: 'hire' },
    { name: 'CONTACT', href: '#contact', icon: '/icons/contact.png' },
    { name: 'PORTFOLIO', href: '#portfolio', icon: '/icons/portfolio.png' },
    { name: 'GET QUOTE', href: '#quote', icon: '/icons/settings.png' }
  ];

  const companySubmenuItems = [
    { name: 'Company Profile', icon: '/icons/company.png', href: '#profile' },
    { name: 'Life @ ColourMoon', icon: '/icons/work-life-balance.png', href: '#life' },
    { name: 'Our Awards', icon: '/icons/trophy.png', href: '#awards' },
    { name: 'Engagement Model', icon: '/icons/enterprice.png', href: '#engagement' },
    { name: 'We Are Hiring?', icon: '/icons/hiring.png', href: '#hiring' },
    { name: 'Our Blog', icon: '/icons/blogging.png', href: '#blog' },
    { name: 'Referral / Partnership', icon: '/icons/referal.png', href: '#referral' },
    { name: 'Our Team', icon: '/icons/team.png', href: '#team' }
  ];

  const servicesSubmenuItems = [
    // Row 1
    { name: 'Mobile App Development', icon: '/icons/mobile-development.png', href: '#mobile-app' },
    { name: 'Web Development', icon: '/icons/web.png', href: '#web-dev' },
    { name: 'Game Development', icon: '/icons/game.png', href: '#game' },
    { name: 'Graphic Designing', icon: '/icons/graphic.png', href: '#graphic' },
    { name: 'Digital Marketing', icon: '/icons/digital-marketing.png', href: '#digital-marketing' },

    // Row 2
    { name: 'Android Applications', icon: '/icons/android_development.png', href: '#android' },
    { name: 'Web For Start-up', icon: '/icons/startup.png', href: '#web-startup' },
    { name: 'Running Game Development', icon: '/icons/running-game.png', href: '#running-game' },
    { name: 'UI/UX Designing', icon: '/icons/uiux.png', href: '#uiux' },
    { name: 'For Start-ups', icon: '/icons/startup.png', href: '#startup-marketing' },

    // Row 3
    { name: 'iOS Applications', icon: '/icons/ios_development.png', href: '#ios' },
    { name: 'Web For Growing Business', icon: '/icons/web-business.png', href: '#web-business' },
    { name: 'Unity 3D Game Development', icon: '/icons/unity.png', href: '#unity' },
    { name: 'Mobile App Prototyping', icon: '/icons/prototype.png', href: '#prototype' },
    { name: 'For Growing Business', icon: '/icons/business-marketing.png', href: '#business-marketing' },

    // Row 4
    { name: 'Cross-Platform Applications', icon: '/icons/cross_platform_development.png', href: '#cross-platform' },
    { name: 'Web For enterprise', icon: '/icons/web-enterprise.png', href: '#web-enterprise' },
    { name: 'Ludo Game Development', icon: '/icons/ludo.png', href: '#ludo' },
    { name: 'Logo Designing', icon: '/icons/logo.png', href: '#logo' },
    { name: 'Search Engine Optimization', icon: '/icons/seo.png', href: '#seo' },

    // Row 5
    { name: 'App For Start-up', icon: '/icons/startup.png', href: '#startup' },
    { name: 'Ecommerce Development', icon: '/icons/ecommerce.png', href: '#ecommerce' },
    { name: 'Tambola Game Development', icon: '/icons/tambola.png', href: '#tambola' },
    { name: 'Digital Cartoon Making', icon: '/icons/cartoon.png', href: '#cartoon' },
    { name: 'Search Engine Marketing', icon: '/icons/sem.png', href: '#sem' },

    // Row 6
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: 'Web App Development', icon: '/icons/webapp.png', href: '#webapp' },
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: 'Social Media Marketing', icon: '/icons/smm.png', href: '#smm' },

    // Row 7
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: 'Website Revamp', icon: '/icons/revamp.png', href: '#revamp' },
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: 'Content Writing', icon: '/icons/content.png', href: '#content' }
  ];

  const technologiesSubmenuItems = [
    // Row 1
    { name: 'Core PHP', icon: '/icons/php.png', href: '#php' },
    { name: 'Codeigniter', icon: '/icons/codeigniter.png', href: '#codeigniter' },
    { name: 'Android Studio', icon: '/icons/android-studio.png', href: '#android-studio' },

    // Row 2
    { name: 'Swift', icon: '/icons/swift.png', href: '#swift' },
    { name: 'Ionic', icon: '/icons/ionic.png', href: '#ionic' },
    { name: 'Bootstrap', icon: '/icons/bootstrap.png', href: '#bootstrap' },

    // Row 3
    { name: 'Angular Js', icon: '/icons/angular.png', href: '#angular' },
    { name: 'Flutter', icon: '/icons/flutter.png', href: '#flutter' },
    { name: 'Wordpress', icon: '/icons/wordpress.png', href: '#wordpress' },

    // Row 4
    { name: 'PWA', icon: '/icons/pwa.png', href: '#pwa' },
    { name: 'AMP', icon: '/icons/amp.png', href: '#amp' },
    { name: 'Unity 3D', icon: '/icons/unity3d.png', href: '#unity3d' }
  ];

  const productsSubmenuItems = [
    // Row 1
    { name: 'Food Delivery App Development', icon: '/icons/food-delivery.png', href: '#food-delivery' },
    { name: 'Taxi Booking App Development', icon: '/icons/taxi-booking.png', href: '#taxi-booking' },
    { name: 'Grocery Delivery App Development', icon: '/icons/grocery-delivery.png', href: '#grocery-delivery' },

    // Row 2
    { name: 'Hotel Booking App Development', icon: '/icons/hotel-booking.png', href: '#hotel-booking' },
    { name: 'Real Estate App Development', icon: '/icons/real-estate.png', href: '#real-estate' },
    { name: 'Logistics App Development', icon: '/icons/logistics.png', href: '#logistics' },

    // Row 3
    { name: 'Doctor & Healthcare App Development', icon: '/icons/healthcare.png', href: '#healthcare' },
    { name: 'OTT App Development', icon: '/icons/ott.png', href: '#ott' },
    { name: 'Social Media App Development', icon: '/icons/social-media.png', href: '#social-media' },

    // Row 4
    { name: 'Multi Vendor Ecommerce App Development', icon: '/icons/multi-vendor.png', href: '#multi-vendor' },
    { name: 'Single Vendor Ecommerce App Development', icon: '/icons/single-vendor.png', href: '#single-vendor' },
    { name: 'Delivery App Development', icon: '/icons/delivery.png', href: '#delivery' },

    // Row 5
    { name: 'E-Learning App Development', icon: '/icons/elearning.png', href: '#elearning' },
    { name: 'Loan Landing App Development', icon: '/icons/loan-landing.png', href: '#loan-landing' },
    { name: 'Beauty Makeup and Salon App Development', icon: '/icons/beauty-salon.png', href: '#beauty-salon' },

    // Row 6
    { name: 'Movie Ticket Booking App Development', icon: '/icons/movie-ticket.png', href: '#movie-ticket' },
    { name: '13 Card Rummy Game App Development', icon: '/icons/rummy-game.png', href: '#rummy-game' },
    { name: 'Temlode / Bingo / Housie Game App Development', icon: '/icons/bingo-game.png', href: '#bingo-game' },

    // Row 7
    { name: 'Short Video Sharing App Development', icon: '/icons/video-sharing.png', href: '#video-sharing' },
    { name: 'Home Service App Development', icon: '/icons/home-service.png', href: '#home-service' },
    { name: 'QR Menu Scanner App Development', icon: '/icons/qr-menu.png', href: '#qr-menu' }
  ];

  const hireSubmenuItems = [
    // Row 1
    { name: 'Hire Web Developers', icon: '/icons/hire-web.png', href: '#hire-web' },
    { name: 'Hire Android Developers', icon: '/icons/hire-android.png', href: '#hire-android' },
    { name: 'Hire iPhone Developers', icon: '/icons/hire-iphone.png', href: '#hire-iphone' },

    // Row 2
    { name: 'Hire Ionic Developers', icon: '/icons/hire-ionic.png', href: '#hire-ionic' },
    { name: 'Hire Angular JS Developers', icon: '/icons/hire-angular.png', href: '#hire-angular' },
    { name: 'Hire PHP Developers', icon: '/icons/hire-php.png', href: '#hire-php' },

    // Row 3
    { name: 'Hire Flutter Developer', icon: '/icons/hire-flutter.png', href: '#hire-flutter' },
    { name: 'Hire Codeigniter Developers', icon: '/icons/hire-codeigniter.png', href: '#hire-codeigniter' },
    { name: 'Hire NodeJS Developers', icon: '/icons/hire-nodejs.png', href: '#hire-nodejs' },

    // Row 4
    { name: 'Hire Unity Developers', icon: '/icons/hire-unity.png', href: '#hire-unity' },
    { name: 'Hire Front-End Developer', icon: '/icons/hire-frontend.png', href: '#hire-frontend' },
    { name: 'Hire Graphic Designer', icon: '/icons/hire-graphic.png', href: '#hire-graphic' },

    // Row 5
    { name: 'Hire Web Designer', icon: '/icons/hire-designer.png', href: '#hire-designer' },
    { name: '', icon: '', href: '' }, // Empty for spacing
    { name: '', icon: '', href: '' } // Empty for spacing
  ];

  return (
    <>
      {/* Top Banner - Hidden when scrolled */}
      <div className={`top-banner transition-all duration-300 ${isScrolled ? 'h-0 overflow-hidden' : 'h-auto'}`}>
        <div className="theme-bg-secondary border-b theme-border-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-10 text-sm">
              <div className="flex items-center space-x-4">
                <span className="theme-text-secondary">
                  A company of Distinction ISO 9001:2015 Certified Company 🏆
                </span>
              </div>
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <span className="theme-text-tertiary">📧</span>
                  <span className="theme-text-secondary">+91 9876 600 666</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="theme-text-tertiary">📱</span>
                  <span className="theme-text-secondary">+971 55 899 4399</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Navbar */}
      <nav className={`main-navbar sticky top-0 z-50 transition-all duration-300 ${
        isScrolled ? 'theme-shadow-lg' : 'theme-shadow-sm'
      }`}>
        <div className={`${getNavbarClass()} border-b border-opacity-30 transition-all duration-500`}>
          <div className="w-full px-4 sm:px-6 lg:px-8">
            <div className="flex items-center h-20 w-full">
              {/* Logo Section */}
              <div className="flex items-center flex-shrink-0" style={{ width: '280px' }}>
                <div className="flex items-center space-x-4">
                  {/* Logo */}
                  <div className="w-14 h-14 rounded-lg overflow-hidden bg-white/10 backdrop-blur-sm">
                    <Image
                      src="/images/BRTLOGO.png"
                      alt="BRTSoft Logo"
                      width={56}
                      height={56}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {/* Company name and tagline */}
                  <div>
                    <h1 className={`text-xl font-bold ${getTextColor()} leading-tight transition-colors duration-300`}>BRTSoft</h1>
                    <p className={`text-xs ${getTextColor()} opacity-80 leading-tight transition-colors duration-300`}>Technology Solutions</p>
                  </div>

                  {/* Tilted Separator Line - Bold */}
                  <div className="h-12 w-0.5 bg-white/50 transform rotate-12 ml-2"></div>
                </div>
              </div>

              {/* Desktop Navigation - Full Width */}
              <div className="hidden lg:flex flex-1 items-center justify-center">
                <div className="flex items-center justify-end space-x-1 w-full">
                  {navItems.map((item, index) => (
                    <div
                      key={item.name}
                      className="relative"
                      onMouseEnter={() => {
                        if (item.hasSubmenu) {
                          if (item.submenuType === 'company') setShowCompanySubmenu(true);
                          if (item.submenuType === 'services') setShowServicesSubmenu(true);
                          if (item.submenuType === 'technologies') setShowTechnologiesSubmenu(true);
                          if (item.submenuType === 'products') setShowProductsSubmenu(true);
                          if (item.submenuType === 'hire') setShowHireSubmenu(true);
                        }
                      }}
                      onMouseLeave={() => {
                        if (item.hasSubmenu) {
                          if (item.submenuType === 'company') setShowCompanySubmenu(false);
                          if (item.submenuType === 'services') setShowServicesSubmenu(false);
                          if (item.submenuType === 'technologies') setShowTechnologiesSubmenu(false);
                          if (item.submenuType === 'products') setShowProductsSubmenu(false);
                          if (item.submenuType === 'hire') setShowHireSubmenu(false);
                        }
                      }}
                    >
                      <a
                        href={item.href}
                        className={`nav-item-green px-2 py-3 text-sm font-medium transition-all duration-300 ${getTextColor()} ${getHoverColor()} rounded-lg group transform hover:scale-105 min-w-0 block`}
                        style={{
                          animationDelay: `${index * 0.1}s`
                        }}
                      >
                        <div className="flex flex-col items-center space-y-1.5">
                          <div className="transition-transform duration-300 group-hover:scale-110">
                            <Image
                              src={item.icon}
                              alt={item.name}
                              width={32}
                              height={32}
                              className="w-8 h-8 object-contain filter brightness-0 invert"
                            />
                          </div>
                          <span className="text-base font-bold tracking-wide uppercase whitespace-nowrap">{item.name}</span>
                        </div>
                      </a>
                    </div>
                  ))}
                </div>
              </div>

              {/* Right side items - Remove extra space */}
              <div className="flex items-center flex-shrink-0">
                {/* Mobile menu button */}
                <div className="lg:hidden">
                  <button
                    type="button"
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="mobile-menu-button text-white hover:text-green-100 focus:outline-none p-2"
                    aria-controls="mobile-menu"
                    aria-expanded={isMobileMenuOpen}
                  >
                    <span className="sr-only">Open main menu</span>
                    {isMobileMenuOpen ? (
                      <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    ) : (
                      <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Company Submenu - Full Width */}
          {showCompanySubmenu && (
            <div
              className="absolute top-full left-0 w-full z-50 transition-all duration-300"
              onMouseEnter={() => setShowCompanySubmenu(true)}
              onMouseLeave={() => setShowCompanySubmenu(false)}
            >
              <div className={`${getNavbarClass()} border-t border-white/20`}>
                <div className="w-full px-8 py-8">
                  <div className="grid grid-cols-4 gap-x-16 gap-y-8 w-full">
                    {companySubmenuItems.map((subItem, index) => (
                      <div key={subItem.name} className="relative">
                        <a
                          href={subItem.href}
                          className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-200 ${getTextColor()} ${getHoverColor()} group w-full`}
                        >
                          <div className="flex-shrink-0">
                            {renderIcon(subItem.icon, subItem.name, 40, 40, "w-10 h-10 object-contain font-bold", true)}
                          </div>
                          <span className="text-lg font-semibold whitespace-nowrap">{subItem.name}</span>
                        </a>
                        {/* Vertical divider - show for all except last column */}
                        {(index + 1) % 4 !== 0 && (
                          <div className={`absolute top-2 -right-8 w-px h-16 ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                        )}
                        {/* Horizontal divider - show for first 4 items */}
                        {index < 4 && (
                          <div className={`absolute -bottom-4 left-4 right-4 h-px ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Services Submenu - Full Width */}
          {showServicesSubmenu && (
            <div
              className="absolute top-full left-0 w-full z-50 transition-all duration-300"
              onMouseEnter={() => setShowServicesSubmenu(true)}
              onMouseLeave={() => setShowServicesSubmenu(false)}
            >
              <div className={`${getNavbarClass()} border-t border-white/20`}>
                <div className="w-full px-8 py-8">
                  <div className="grid grid-cols-5 gap-x-12 gap-y-6 w-full">
                    {servicesSubmenuItems.map((subItem, index) => (
                      subItem.name ? (
                        <div key={subItem.name} className="relative">
                          <a
                            href={subItem.href}
                            className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${getTextColor()} ${getHoverColor()} group w-full`}
                          >
                            <div className="flex-shrink-0">
                              {renderIcon(subItem.icon, subItem.name, 40, 40, "w-10 h-10 object-contain font-bold", true)}
                            </div>
                            <span className="text-lg font-semibold whitespace-nowrap">{subItem.name}</span>
                          </a>
                          {/* Vertical divider - show for all except last column */}
                          {(index + 1) % 5 !== 0 && (
                            <div className={`absolute top-2 -right-6 w-px h-16 ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                          )}
                          {/* Horizontal divider - show for first 5 items */}
                          {index < 5 && (
                            <div className={`absolute -bottom-3 left-3 right-3 h-px ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                          )}
                        </div>
                      ) : (
                        <div key={index} className="w-full"></div>
                      )
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Technologies Submenu - Full Width */}
          {showTechnologiesSubmenu && (
            <div
              className="absolute top-full left-0 w-full z-50 transition-all duration-300"
              onMouseEnter={() => setShowTechnologiesSubmenu(true)}
              onMouseLeave={() => setShowTechnologiesSubmenu(false)}
            >
              <div className={`${getNavbarClass()} border-t border-white/20`}>
                <div className="w-full px-8 py-8">
                  <div className="grid grid-cols-3 gap-x-20 gap-y-8 w-full">
                    {technologiesSubmenuItems.map((subItem, index) => (
                      <div key={subItem.name} className="relative">
                        <a
                          href={subItem.href}
                          className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-200 ${getTextColor()} ${getHoverColor()} group w-full`}
                        >
                          <div className="flex-shrink-0">
                            {renderIcon(subItem.icon, subItem.name, 40, 40, "w-10 h-10 object-contain font-bold", true)}
                          </div>
                          <span className="text-lg font-semibold whitespace-nowrap">{subItem.name}</span>
                        </a>
                        {/* Vertical divider - show for all except last column */}
                        {(index + 1) % 3 !== 0 && (
                          <div className={`absolute top-2 -right-10 w-px h-16 ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                        )}
                        {/* Horizontal divider - show for first 3 items */}
                        {index < 3 && (
                          <div className={`absolute -bottom-4 left-4 right-4 h-px ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Products Submenu - Full Width */}
          {showProductsSubmenu && (
            <div
              className="absolute top-full left-0 w-full z-50 transition-all duration-300"
              onMouseEnter={() => setShowProductsSubmenu(true)}
              onMouseLeave={() => setShowProductsSubmenu(false)}
            >
              <div className={`${getNavbarClass()} border-t border-white/20`}>
                <div className="w-full px-8 py-8">
                  <div className="grid grid-cols-3 gap-x-16 gap-y-6 w-full">
                    {productsSubmenuItems.map((subItem, index) => (
                      <div key={subItem.name} className="relative">
                        <a
                          href={subItem.href}
                          className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${getTextColor()} ${getHoverColor()} group w-full`}
                        >
                          <div className="flex-shrink-0">
                            {renderIcon(subItem.icon, subItem.name, 40, 40, "w-10 h-10 object-contain font-bold", true)}
                          </div>
                          <span className="text-lg font-semibold whitespace-nowrap">{subItem.name}</span>
                        </a>
                        {/* Vertical divider - show for all except last column */}
                        {(index + 1) % 3 !== 0 && (
                          <div className={`absolute top-2 -right-8 w-px h-16 ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                        )}
                        {/* Horizontal divider - show for first 3 items */}
                        {index < 3 && (
                          <div className={`absolute -bottom-3 left-3 right-3 h-px ${currentTheme === 'dark' ? 'bg-white/20' : 'bg-gray-300'}`}></div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Hire Submenu - Full Width */}
          {showHireSubmenu && (
            <div
              className="absolute top-full left-0 w-full z-50 transition-all duration-300"
              onMouseEnter={() => setShowHireSubmenu(true)}
              onMouseLeave={() => setShowHireSubmenu(false)}
            >
              <div className={`${getNavbarClass()} border-t border-white/20`}>
                <div className="w-full px-8 py-8">
                  <div className="grid grid-cols-3 gap-x-16 gap-y-8 w-full">
                    {hireSubmenuItems.map((subItem, index) => (
                      subItem.name ? (
                        <a
                          key={subItem.name + index}
                          href={subItem.href}
                          className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-200 ${getTextColor()} ${getHoverColor()} group w-full`}
                        >
                          <div className="flex-shrink-0">
                            {renderIcon(subItem.icon, subItem.name, 40, 40, "w-10 h-10 object-contain font-bold", true)}
                          </div>
                          <span className="text-lg font-semibold whitespace-nowrap">{subItem.name}</span>
                        </a>
                      ) : (
                        <div key={index} className="w-full"></div>
                      )
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Hire Submenu - Full Width */}
          {showHireSubmenu && (
            <div
              className="absolute top-full left-0 w-full z-50 transition-all duration-300"
              onMouseEnter={() => setShowHireSubmenu(true)}
              onMouseLeave={() => setShowHireSubmenu(false)}
            >
              <div className={`${getNavbarClass()} border-t border-white/20`}>
                <div className="w-full px-8 py-8">
                  <div className="grid grid-cols-3 gap-x-16 gap-y-8 w-full">
                    {hireSubmenuItems.map((subItem, index) => (
                      subItem.name ? (
                        <a
                          key={subItem.name + index}
                          href={subItem.href}
                          className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-200 ${getTextColor()} ${getHoverColor()} group w-full`}
                        >
                          <div className="flex-shrink-0">
                            {renderIcon(subItem.icon, subItem.name, 40, 40, "w-10 h-10 object-contain font-bold", true)}
                          </div>
                          <span className="text-lg font-semibold whitespace-nowrap">{subItem.name}</span>
                        </a>
                      ) : (
                        <div key={index} className="w-full"></div>
                      )
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <div className="lg:hidden" id="mobile-menu">
              <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 theme-bg-secondary border-t theme-border-primary">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium theme-text-secondary hover:theme-text-primary hover:theme-bg-tertiary transition-colors duration-200"
                  >
                    <span className="flex items-center space-x-2">
                      <span>{item.icon}</span>
                      <span>{item.name}</span>
                    </span>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </nav>
    </>
  );
};

export default Navbar;
