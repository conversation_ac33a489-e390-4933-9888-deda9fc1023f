'use client';

import { useTheme as useThemeContext } from './ThemeContext';

/**
 * Enhanced theme hook with additional utilities
 * Re-exports the base useTheme hook with additional helper functions
 */
export const useTheme = () => {
  const theme = useThemeContext();

  /**
   * Get a specific theme token value
   * @param {string} path - Dot notation path to the token (e.g., 'background.primary')
   * @returns {string|undefined} Token value or undefined if not found
   */
  const getToken = (path) => {
    const keys = path.split('.');
    let value = theme.tokens;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return undefined;
      }
    }
    
    return value;
  };

  /**
   * Get CSS variable name for a theme token
   * @param {string} path - Dot notation path to the token
   * @returns {string} CSS variable name
   */
  const getCSSVar = (path) => {
    return `var(--theme-${path.replace(/\./g, '-')})`;
  };

  /**
   * Create a style object with theme tokens
   * @param {Object} styles - Style object with theme token paths as values
   * @returns {Object} Style object with resolved values
   */
  const createStyles = (styles) => {
    const resolvedStyles = {};
    
    Object.entries(styles).forEach(([property, tokenPath]) => {
      if (typeof tokenPath === 'string' && tokenPath.startsWith('theme.')) {
        const path = tokenPath.replace('theme.', '');
        resolvedStyles[property] = getToken(path);
      } else {
        resolvedStyles[property] = tokenPath;
      }
    });
    
    return resolvedStyles;
  };

  /**
   * Get theme-aware class names
   * @param {Object} classes - Object with light and dark class names
   * @returns {string} Appropriate class name for current theme
   */
  const getThemeClass = (classes) => {
    if (typeof classes === 'string') {
      return classes;
    }
    
    if (typeof classes === 'object' && classes !== null) {
      if (theme.isDark && classes.dark) {
        return classes.dark;
      }
      if (theme.isLight && classes.light) {
        return classes.light;
      }
      return classes.default || '';
    }
    
    return '';
  };

  /**
   * Conditional theme values
   * @param {*} lightValue - Value for light theme
   * @param {*} darkValue - Value for dark theme
   * @returns {*} Appropriate value for current theme
   */
  const themeValue = (lightValue, darkValue) => {
    return theme.isDark ? darkValue : lightValue;
  };

  /**
   * Get theme-specific icon or component
   * @param {Object} components - Object with light and dark components
   * @returns {*} Appropriate component for current theme
   */
  const getThemeComponent = (components) => {
    if (theme.isDark && components.dark) {
      return components.dark;
    }
    if (theme.isLight && components.light) {
      return components.light;
    }
    return components.default || null;
  };

  /**
   * Create theme-aware inline styles
   * @param {Object} baseStyles - Base styles object
   * @param {Object} themeOverrides - Theme-specific style overrides
   * @returns {Object} Merged styles object
   */
  const createThemeStyles = (baseStyles = {}, themeOverrides = {}) => {
    const overrides = theme.isDark ? themeOverrides.dark : themeOverrides.light;
    return { ...baseStyles, ...overrides };
  };

  /**
   * Get contrast color for better readability
   * @param {string} backgroundColor - Background color token path
   * @returns {string} Appropriate text color
   */
  const getContrastColor = (backgroundColor) => {
    // This is a simplified version - you might want to implement
    // a more sophisticated contrast calculation
    const bgColor = getToken(backgroundColor);
    
    if (!bgColor) {
      return getToken('text.primary');
    }
    
    // Simple heuristic based on common color patterns
    if (bgColor.includes('50') || bgColor.includes('100') || bgColor.includes('200')) {
      return getToken('text.primary');
    }
    
    if (bgColor.includes('800') || bgColor.includes('900') || bgColor.includes('950')) {
      return getToken('text.inverse');
    }
    
    return theme.isDark ? getToken('text.inverse') : getToken('text.primary');
  };

  /**
   * Generate theme-aware Tailwind classes
   * @param {string} baseClasses - Base Tailwind classes
   * @param {Object} themeClasses - Theme-specific class overrides
   * @returns {string} Combined class string
   */
  const cn = (baseClasses = '', themeClasses = {}) => {
    const themeSpecific = theme.isDark ? themeClasses.dark : themeClasses.light;
    return [baseClasses, themeSpecific].filter(Boolean).join(' ');
  };

  return {
    // Re-export all original theme context values
    ...theme,
    
    // Additional utility functions
    getToken,
    getCSSVar,
    createStyles,
    getThemeClass,
    themeValue,
    getThemeComponent,
    createThemeStyles,
    getContrastColor,
    cn
  };
};

/**
 * Hook for theme-aware media queries
 * @returns {Object} Media query utilities
 */
export const useThemeMediaQuery = () => {
  const { resolvedTheme } = useTheme();

  return {
    isDarkMode: resolvedTheme === 'dark',
    isLightMode: resolvedTheme === 'light',
    prefersReducedMotion: typeof window !== 'undefined' 
      ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
      : false,
    prefersHighContrast: typeof window !== 'undefined'
      ? window.matchMedia('(prefers-contrast: high)').matches
      : false
  };
};

/**
 * Hook for theme transitions and animations
 * @returns {Object} Animation utilities
 */
export const useThemeAnimation = () => {
  const { tokens, resolvedTheme } = useTheme();

  const getTransition = (property = 'all', duration = 'normal') => {
    const durationValue = tokens.animation?.duration?.[duration] || '300ms';
    const easing = tokens.animation?.easing?.easeInOut || 'ease-in-out';
    return `${property} ${durationValue} ${easing}`;
  };

  const getThemeTransition = () => {
    return getTransition('background-color, color, border-color', 'fast');
  };

  return {
    getTransition,
    getThemeTransition,
    currentTheme: resolvedTheme
  };
};

export default useTheme;
