import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business", 
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "#f97316" },
    { name: "MOBILE APPS", color: "#14b8a6" },
    { name: "DIGITAL MARKETING", color: "#a855f7" },
    { name: "BRAND IDENTITY", color: "#3b82f6" }
  ];

  return (
    <section style={{
      padding: '5rem 0',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 2rem'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h2 style={{
            fontSize: '2.75rem',
            fontWeight: 'bold',
            color: '#1a1a1a',
            marginBottom: '1rem',
            fontFamily: 'system-ui, sans-serif',
            letterSpacing: '-0.025em'
          }}>
            Our Specialized Services
          </h2>
          <p style={{
            color: '#666666',
            fontSize: '1.1rem',
            marginBottom: '2.5rem',
            fontFamily: 'system-ui, sans-serif'
          }}>
            We Develop all your Digital Needs
          </p>

          {/* Category Pills */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            flexWrap: 'wrap',
            marginBottom: '4rem'
          }}>
            {categories.map((category, index) => (
              <span
                key={index}
                style={{
                  backgroundColor: category.color,
                  color: '#ffffff',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  fontSize: '0.8rem',
                  fontWeight: '600',
                  fontFamily: 'system-ui, sans-serif',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Main Container - Exact Reference Match */}
        <div style={{
          maxWidth: '1000px',
          margin: '0 auto',
          position: 'relative'
        }}>
          <div style={{
            backgroundColor: '#ffffff',
            borderRadius: '20px',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)',
            overflow: 'hidden',
            position: 'relative',
            minHeight: '650px'
          }}>

            {/* Orange Cross Background - Exact Reference Design */}
            <div style={{ position: 'absolute', inset: '0', zIndex: 1 }}>
              {/* Vertical Orange Bar */}
              <div style={{
                position: 'absolute',
                top: '0',
                bottom: '0',
                left: '50%',
                width: '100px',
                transform: 'translateX(-50%)',
                background: 'linear-gradient(180deg, #ff6b35 0%, #ff8c42 25%, #ffa726 50%, #ff8c42 75%, #ff6b35 100%)',
                opacity: '0.9'
              }}></div>

              {/* Horizontal Orange Bar */}
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '0',
                right: '0',
                height: '100px',
                transform: 'translateY(-50%)',
                background: 'linear-gradient(90deg, #ff6b35 0%, #ff8c42 25%, #ffa726 50%, #ff8c42 75%, #ff6b35 100%)',
                opacity: '0.9'
              }}></div>
            </div>

            {/* Services Grid - 2x2 Layout like Reference */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gridTemplateRows: '1fr 1fr',
              height: '650px',
              position: 'relative',
              zIndex: 2
            }}>
              {services.map((service) => (
                <div key={service.id} style={{
                  padding: '3rem 2.5rem',
                  backgroundColor: 'transparent',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  position: 'relative'
                }}>
                  <div>
                    {/* Icon */}
                    <div style={{ marginBottom: '2rem' }}>
                      <div style={{
                        width: '70px',
                        height: '70px',
                        backgroundColor: 'rgba(255, 247, 237, 0.8)',
                        borderRadius: '16px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.2)'
                      }}>
                        <Image
                          src={service.icon}
                          alt={service.title}
                          width={36}
                          height={36}
                          style={{ width: '36px', height: '36px', objectFit: 'contain' }}
                        />
                      </div>
                    </div>

                    {/* Title */}
                    <h3 style={{
                      fontSize: '1.4rem',
                      fontWeight: '700',
                      color: '#1a1a1a',
                      marginBottom: '1.25rem',
                      fontFamily: 'system-ui, sans-serif',
                      lineHeight: '1.3'
                    }}>
                      {service.title}
                    </h3>

                    {/* Description */}
                    <p style={{
                      color: '#555555',
                      fontSize: '0.9rem',
                      lineHeight: '1.6',
                      fontFamily: 'system-ui, sans-serif',
                      marginBottom: '1.5rem'
                    }}>
                      {service.description}
                    </p>
                  </div>

                  {/* Gradient Button - Exact Reference Match */}
                  <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
                    <button style={{
                      width: '45px',
                      height: '45px',
                      background: 'linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ffa726 100%)',
                      color: '#ffffff',
                      borderRadius: '50%',
                      border: 'none',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      fontSize: '1.3rem',
                      fontWeight: 'bold',
                      boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
                      transition: 'all 0.3s ease',
                      transform: 'translateY(0)'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.transform = 'translateY(-2px)';
                      e.target.style.boxShadow = '0 6px 20px rgba(255, 107, 53, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.transform = 'translateY(0)';
                      e.target.style.boxShadow = '0 4px 15px rgba(255, 107, 53, 0.3)';
                    }}
                    >
                      →
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
