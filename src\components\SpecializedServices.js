import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business",
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    },
    {
      id: 5,
      title: "Web App Development",
      description: "Build your business success with web applications that help boost all your work helps. The web apps developed by our developers will meet all your requirements and help achieve your business goals.",
      icon: "/icons/app-development.png"
    },
    {
      id: 6,
      title: "Website Revamp",
      description: "Give your website a fresh new look and attract more customers. We can help your website get a complete look with advanced new and fresh developments.",
      icon: "/icons/revamp.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section style={{ padding: '4rem 0', backgroundColor: '#ffffff', position: 'relative' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h2 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            color: '#000000',
            marginBottom: '0.75rem',
            fontFamily: 'system-ui, sans-serif'
          }}>
            Our Specialized Services
          </h2>
          <p style={{
            color: '#6b7280',
            fontSize: '1rem',
            marginBottom: '2rem',
            fontFamily: 'system-ui, sans-serif'
          }}>
            We Develop all your Digital Needs
          </p>

          {/* Category Pills */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '0.75rem',
            flexWrap: 'wrap',
            marginBottom: '3rem'
          }}>
            {categories.map((category, index) => (
              <span
                key={index}
                style={{
                  backgroundColor: category.name === 'WEB DEVELOPMENT' ? '#f97316' :
                                 category.name === 'MOBILE APPS' ? '#14b8a6' :
                                 category.name === 'DIGITAL MARKETING' ? '#a855f7' : '#3b82f6',
                  color: '#ffffff',
                  padding: '0.5rem 1.25rem',
                  borderRadius: '9999px',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  fontFamily: 'system-ui, sans-serif'
                }}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid with Orange Cross - Exact Reference Match */}
        <div className="max-w-5xl mx-auto relative">
          <div className="relative bg-white overflow-hidden" style={{ minHeight: '700px' }}>

            {/* Orange Cross Background - Exact Reference Colors */}
            <div className="absolute inset-0">
              {/* Vertical Orange Strip */}
              <div
                className="absolute top-0 bottom-0"
                style={{
                  left: '50%',
                  width: '60px',
                  transform: 'translateX(-50%)',
                  background: 'linear-gradient(180deg, #ff8c42 0%, #ffa726 50%, #ff8c42 100%)'
                }}
              ></div>

              {/* Horizontal Orange Strips */}
              <div
                className="absolute left-0 right-0"
                style={{
                  top: '33.33%',
                  height: '60px',
                  transform: 'translateY(-50%)',
                  background: 'linear-gradient(90deg, #ff8c42 0%, #ffa726 50%, #ff8c42 100%)'
                }}
              ></div>
              <div
                className="absolute left-0 right-0"
                style={{
                  top: '66.66%',
                  height: '60px',
                  transform: 'translateY(-50%)',
                  background: 'linear-gradient(90deg, #ff8c42 0%, #ffa726 50%, #ff8c42 100%)'
                }}
              ></div>
            </div>

            {/* Services Grid - Exact Reference Layout */}
            <div className="grid grid-cols-2 grid-rows-3 h-full relative z-10">
              {services.map((service) => (
                <div
                  key={service.id}
                  className="p-6 bg-white relative flex flex-col"
                  style={{ minHeight: '230px' }}
                >
                  {/* Icon */}
                  <div className="mb-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Image
                        src={service.icon}
                        alt={service.title}
                        width={24}
                        height={24}
                        className="w-6 h-6 object-contain"
                      />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-lg font-bold text-black mb-3 leading-tight">
                    {service.title}
                  </h3>

                  <p className="text-gray-600 text-xs leading-relaxed mb-4 flex-grow">
                    {service.description}
                  </p>

                  {/* Button */}
                  <div className="flex justify-end">
                    <button className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold">→</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
