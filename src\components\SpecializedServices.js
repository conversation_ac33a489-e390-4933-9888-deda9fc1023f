import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business",
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    },
    {
      id: 5,
      title: "Web App Development",
      description: "Build your business success with web applications that help boost all your work helps. The web apps developed by our developers will meet all your requirements and help achieve your business goals.",
      icon: "/icons/app-development.png"
    },
    {
      id: 6,
      title: "Website Revamp",
      description: "Give your website a fresh new look and attract more customers. We can help your website get a complete look with advanced new and fresh developments.",
      icon: "/icons/revamp.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section className="py-16 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Our Specialized Services</h2>
          <p className="text-gray-600 text-lg mb-8">We Develop all your Digital Needs</p>

          {/* Category Pills */}
          <div className="flex justify-center gap-4 flex-wrap">
            {categories.map((category, index) => (
              <span
                key={index}
                className={`${category.color} text-white px-6 py-3 rounded-full text-sm font-semibold shadow-lg hover:shadow-xl transition-shadow duration-300`}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid - 2 columns, 3 rows */}
        <div className="max-w-6xl mx-auto relative">
          {/* Background Gradient Sections */}
          <div className="absolute inset-0 grid grid-cols-2 gap-8">
            {/* Left Column Background */}
            <div className="bg-gradient-to-br from-orange-100 to-orange-200 rounded-3xl opacity-30"></div>
            {/* Right Column Background */}
            <div className="bg-gradient-to-br from-yellow-100 to-orange-200 rounded-3xl opacity-30"></div>
          </div>

          {/* Vertical Divider */}
          <div className="absolute left-1/2 top-0 bottom-0 w-8 bg-gradient-to-b from-orange-300 via-yellow-400 to-orange-400 transform -translate-x-1/2 rounded-full opacity-80"></div>

          {/* Horizontal Dividers */}
          <div className="absolute top-1/3 left-0 right-0 h-8 bg-gradient-to-r from-orange-300 via-yellow-400 to-orange-400 opacity-60 rounded-full"></div>
          <div className="absolute top-2/3 left-0 right-0 h-8 bg-gradient-to-r from-orange-300 via-yellow-400 to-orange-400 opacity-60 rounded-full"></div>

          <div className="grid grid-cols-2 gap-8 relative z-10">
            {services.map((service, index) => (
              <div
                key={service.id}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 relative group border border-gray-100"
              >
                {/* Icon */}
                <div className="mb-6 flex justify-center">
                  <div className="w-20 h-20 bg-orange-50 rounded-full flex items-center justify-center group-hover:bg-orange-100 transition-colors duration-300 border-2 border-orange-200">
                    <Image
                      src={service.icon}
                      alt={service.title}
                      width={40}
                      height={40}
                      className="w-10 h-10 object-contain filter brightness-0 opacity-70"
                    />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-800 mb-4 text-center group-hover:text-orange-600 transition-colors duration-300">
                  {service.title}
                </h3>

                <p className="text-gray-600 text-sm leading-relaxed mb-6 text-center">
                  {service.description}
                </p>

                {/* Button */}
                <div className="flex justify-center">
                  <button className="w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl">
                    <span className="text-xl font-bold">→</span>
                  </button>
                </div>

                {/* Hover Border Effect */}
                <div className="absolute inset-0 border-2 border-transparent group-hover:border-orange-300 rounded-2xl transition-colors duration-300"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
