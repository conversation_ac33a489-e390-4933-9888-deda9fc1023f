import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png",
      buttonText: "→"
    },
    {
      id: 2,
      title: "Web For Growing Business",
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png",
      buttonText: "→"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png",
      buttonText: "→"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png",
      buttonText: "→"
    },
    {
      id: 5,
      title: "Web App Development",
      description: "Build your business success with web applications that help boost all your work helps. The web apps developed by our developers will meet all your requirements and help achieve your business goals.",
      icon: "/icons/app-development.png",
      buttonText: "→"
    },
    {
      id: 6,
      title: "Website Revamp",
      description: "Give your website a fresh new look and attract more customers. We can help your website get a complete look with advanced new and fresh developments.",
      icon: "/icons/revamp.png",
      buttonText: "→"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section className="py-16 bg-gray-50 relative overflow-hidden">
      {/* Background Design Elements */}
      <div className="absolute top-0 right-0 w-1/2 h-full">
        <div className="absolute top-20 right-10 w-96 h-96 bg-gradient-to-br from-orange-300 to-orange-500 rounded-full opacity-20"></div>
        <div className="absolute bottom-20 right-20 w-64 h-64 bg-gradient-to-br from-yellow-300 to-orange-400 rounded-full opacity-30"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Our Specialized Services</h2>
          <p className="text-gray-600 text-lg">We Develop all your Digital Needs</p>
          
          {/* Category Pills */}
          <div className="flex justify-center gap-4 mt-8 flex-wrap">
            {categories.map((category, index) => (
              <span
                key={index}
                className={`${category.color} text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg hover:shadow-xl transition-shadow duration-300`}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {services.map((service) => (
            <div
              key={service.id}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 relative group"
            >
              {/* Icon */}
              <div className="mb-6">
                <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center group-hover:bg-orange-200 transition-colors duration-300">
                  <Image
                    src={service.icon}
                    alt={service.title}
                    width={32}
                    height={32}
                    className="w-8 h-8 object-contain filter brightness-0 opacity-70"
                  />
                </div>
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-orange-600 transition-colors duration-300">
                {service.title}
              </h3>
              
              <p className="text-gray-600 text-sm leading-relaxed mb-6 line-clamp-4">
                {service.description}
              </p>

              {/* Button */}
              <div className="flex justify-end">
                <button className="w-10 h-10 bg-orange-500 hover:bg-orange-600 text-white rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl">
                  <span className="text-lg font-bold">{service.buttonText}</span>
                </button>
              </div>

              {/* Hover Border Effect */}
              <div className="absolute inset-0 border-2 border-transparent group-hover:border-orange-200 rounded-2xl transition-colors duration-300"></div>
            </div>
          ))}
        </div>

        {/* Bottom Decorative Element */}
        <div className="mt-16 flex justify-center">
          <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-yellow-400 rounded-full"></div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
