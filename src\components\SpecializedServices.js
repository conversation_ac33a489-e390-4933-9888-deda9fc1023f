import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business",
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    },
    {
      id: 5,
      title: "Web App Development",
      description: "Build your business success with web applications that help boost all your work helps. The web apps developed by our developers will meet all your requirements and help achieve your business goals.",
      icon: "/icons/app-development.png"
    },
    {
      id: 6,
      title: "Website Revamp",
      description: "Give your website a fresh new look and attract more customers. We can help your website get a complete look with advanced new and fresh developments.",
      icon: "/icons/revamp.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section className="py-20 bg-gray-100 relative">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-3">Our Specialized Services</h2>
          <p className="text-gray-600 text-lg mb-10">We Develop all your Digital Needs</p>

          {/* Category Pills */}
          <div className="flex justify-center gap-4 flex-wrap mb-16">
            {categories.map((category, index) => (
              <span
                key={index}
                className={`${category.color} text-white px-6 py-3 rounded-full text-sm font-semibold shadow-lg`}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid with Orange Cross */}
        <div className="max-w-6xl mx-auto relative">
          {/* Background Container */}
          <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden" style={{ minHeight: '800px' }}>

            {/* Orange Cross Dividers */}
            <div className="absolute inset-0 z-10">
              {/* Vertical Orange Divider */}
              <div
                className="absolute top-0 bottom-0 bg-gradient-to-b from-orange-400 via-yellow-400 to-orange-500"
                style={{
                  left: '50%',
                  width: '60px',
                  transform: 'translateX(-50%)',
                  borderRadius: '0 0 30px 30px'
                }}
              ></div>

              {/* Horizontal Orange Dividers */}
              <div
                className="absolute left-0 right-0 bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500"
                style={{
                  top: '33.33%',
                  height: '60px',
                  transform: 'translateY(-50%)'
                }}
              ></div>
              <div
                className="absolute left-0 right-0 bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500"
                style={{
                  top: '66.66%',
                  height: '60px',
                  transform: 'translateY(-50%)'
                }}
              ></div>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-2 grid-rows-3 h-full relative z-20">
              {services.map((service, index) => (
                <div
                  key={service.id}
                  className="p-8 bg-white relative flex flex-col justify-between"
                  style={{ minHeight: '260px' }}
                >
                  <div>
                    {/* Icon */}
                    <div className="mb-6">
                      <div className="w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center">
                        <Image
                          src={service.icon}
                          alt={service.title}
                          width={32}
                          height={32}
                          className="w-8 h-8 object-contain"
                        />
                      </div>
                    </div>

                    {/* Content */}
                    <h3 className="text-xl font-bold text-gray-800 mb-4 leading-tight">
                      {service.title}
                    </h3>

                    <p className="text-gray-600 text-sm leading-relaxed pr-4">
                      {service.description}
                    </p>
                  </div>

                  {/* Button */}
                  <div className="flex justify-end mt-6">
                    <button className="w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                      <span className="text-xl font-bold">→</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
