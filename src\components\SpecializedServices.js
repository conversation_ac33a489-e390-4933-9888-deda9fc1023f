import React from 'react';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web Development",
      description: "Modern web applications",
      icon: "🌐",
      color: "text-cyan-500"
    },
    {
      id: 2,
      title: "Mobile Apps",
      description: "iOS and Android development",
      icon: "📱",
      color: "text-blue-600"
    },
    {
      id: 3,
      title: "Cloud Solutions",
      description: "Scalable cloud infrastructure",
      icon: "☁️",
      color: "text-gray-500"
    },
    {
      id: 4,
      title: "Consulting",
      description: "Technology consulting services",
      icon: "💡",
      color: "text-yellow-500"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section className="py-16 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Our Specialized Services</h2>
          <p className="text-gray-600 text-lg mb-8">We Develop all your Digital Needs</p>

          {/* Category Pills */}
          <div className="flex justify-center gap-4 flex-wrap">
            {categories.map((category, index) => (
              <span
                key={index}
                className={`${category.color} text-white px-6 py-3 rounded-full text-sm font-semibold shadow-lg hover:shadow-xl transition-shadow duration-300`}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid - 2 columns, 3 rows */}
        <div className="max-w-6xl mx-auto relative">
          {/* Background Gradient Sections */}
          <div className="absolute inset-0 grid grid-cols-2 gap-8">
            {/* Left Column Background */}
            <div className="bg-gradient-to-br from-orange-100 to-orange-200 rounded-3xl opacity-30"></div>
            {/* Right Column Background */}
            <div className="bg-gradient-to-br from-yellow-100 to-orange-200 rounded-3xl opacity-30"></div>
          </div>

          {/* Vertical Divider */}
          <div className="absolute left-1/2 top-0 bottom-0 w-8 bg-gradient-to-b from-orange-300 via-yellow-400 to-orange-400 transform -translate-x-1/2 rounded-full opacity-80"></div>

          {/* Horizontal Dividers */}
          <div className="absolute top-1/3 left-0 right-0 h-8 bg-gradient-to-r from-orange-300 via-yellow-400 to-orange-400 opacity-60 rounded-full"></div>
          <div className="absolute top-2/3 left-0 right-0 h-8 bg-gradient-to-r from-orange-300 via-yellow-400 to-orange-400 opacity-60 rounded-full"></div>

          <div className="grid grid-cols-2 gap-8 relative z-10">
            {services.map((service, index) => (
              <div
                key={service.id}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 relative group border border-gray-100"
              >
                {/* Icon */}
                <div className="mb-6 flex justify-center">
                  <div className="w-20 h-20 bg-orange-50 rounded-full flex items-center justify-center group-hover:bg-orange-100 transition-colors duration-300 border-2 border-orange-200">
                    <Image
                      src={service.icon}
                      alt={service.title}
                      width={40}
                      height={40}
                      className="w-10 h-10 object-contain filter brightness-0 opacity-70"
                    />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-800 mb-4 text-center group-hover:text-orange-600 transition-colors duration-300">
                  {service.title}
                </h3>

                <p className="text-gray-600 text-sm leading-relaxed mb-6 text-center">
                  {service.description}
                </p>

                {/* Button */}
                <div className="flex justify-center">
                  <button className="w-12 h-12 bg-orange-500 hover:bg-orange-600 text-white rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl">
                    <span className="text-xl font-bold">→</span>
                  </button>
                </div>

                {/* Hover Border Effect */}
                <div className="absolute inset-0 border-2 border-transparent group-hover:border-orange-300 rounded-2xl transition-colors duration-300"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
