import React from 'react';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web Development",
      description: "Modern web applications",
      icon: "🌐",
      color: "text-cyan-500"
    },
    {
      id: 2,
      title: "Mobile Apps",
      description: "iOS and Android development",
      icon: "📱",
      color: "text-blue-600"
    },
    {
      id: 3,
      title: "Cloud Solutions",
      description: "Scalable cloud infrastructure",
      icon: "☁️",
      color: "text-gray-500"
    },
    {
      id: 4,
      title: "Consulting",
      description: "Technology consulting services",
      icon: "💡",
      color: "text-yellow-500"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section className="py-16 bg-gray-50 relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-2">Our Specialized Services</h2>
          <p className="text-gray-600 text-base mb-8">We Develop all your Digital Needs</p>

          {/* Category Pills */}
          <div className="flex justify-center gap-3 flex-wrap mb-12">
            {categories.map((category, index) => (
              <span
                key={index}
                className={`${category.color} text-white px-5 py-2 rounded-full text-sm font-medium shadow-md`}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid Container */}
        <div className="max-w-7xl mx-auto relative">
          {/* Main Grid with Orange Cross Dividers */}
          <div className="relative bg-white rounded-3xl overflow-hidden shadow-xl">
            {/* Orange Cross Background */}
            <div className="absolute inset-0">
              {/* Vertical Orange Strip */}
              <div className="absolute left-1/2 top-0 bottom-0 w-16 bg-gradient-to-b from-orange-400 via-yellow-400 to-orange-500 transform -translate-x-1/2"></div>
              {/* Horizontal Orange Strip */}
              <div className="absolute top-1/2 left-0 right-0 h-16 bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500 transform -translate-y-1/2"></div>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-2 relative z-10">
              {services.map((service, index) => (
                <div
                  key={service.id}
                  className={`p-8 bg-white relative ${
                    index === 0 ? 'rounded-tl-3xl' :
                    index === 1 ? 'rounded-tr-3xl' :
                    index === 2 ? '' :
                    index === 3 ? '' :
                    index === 4 ? 'rounded-bl-3xl' :
                    'rounded-br-3xl'
                  }`}
                  style={{ minHeight: '280px' }}
                >
                  {/* Icon */}
                  <div className="mb-6">
                    <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center">
                      <Image
                        src={service.icon}
                        alt={service.title}
                        width={32}
                        height={32}
                        className="w-8 h-8 object-contain"
                      />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-lg font-bold text-gray-800 mb-3">
                    {service.title}
                  </h3>

                  <p className="text-gray-600 text-sm leading-relaxed mb-6">
                    {service.description}
                  </p>

                  {/* Button */}
                  <div className="absolute bottom-6 right-6">
                    <button className="w-10 h-10 bg-orange-500 hover:bg-orange-600 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg">
                      <span className="text-lg font-bold">→</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
