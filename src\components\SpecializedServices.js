import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business",
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    },
    {
      id: 5,
      title: "Web App Development",
      description: "Build your business success with web applications that help boost all your work helps. The web apps developed by our developers will meet all your requirements and help achieve your business goals.",
      icon: "/icons/app-development.png"
    },
    {
      id: 6,
      title: "Website Revamp",
      description: "Give your website a fresh new look and attract more customers. We can help your website get a complete look with advanced new and fresh developments.",
      icon: "/icons/revamp.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "bg-orange-500" },
    { name: "MOBILE APPS", color: "bg-teal-500" },
    { name: "DIGITAL MARKETING", color: "bg-purple-500" },
    { name: "BRAND IDENTITY", color: "bg-blue-500" }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100 relative min-h-screen">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-5xl font-extrabold text-gray-900 mb-4 tracking-tight">
            Our Specialized Services
          </h2>
          <p className="text-gray-600 text-xl mb-8 font-medium">
            We Develop all your Digital Needs
          </p>

          {/* Category Pills */}
          <div className="flex justify-center gap-3 flex-wrap mb-12">
            {categories.map((category, index) => (
              <span
                key={index}
                className={`${category.color} text-white px-8 py-3 rounded-full text-sm font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer`}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Services Grid with Orange Cross */}
        <div className="max-w-6xl mx-auto relative">
          {/* Background Container */}
          <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-200" style={{ minHeight: '900px' }}>

            {/* Orange Cross Dividers */}
            <div className="absolute inset-0 z-10">
              {/* Vertical Orange Divider */}
              <div
                className="absolute top-0 bottom-0 bg-gradient-to-b from-orange-400 via-yellow-400 to-orange-500 shadow-lg"
                style={{
                  left: '50%',
                  width: '80px',
                  transform: 'translateX(-50%)',
                  borderRadius: '0 0 40px 40px'
                }}
              ></div>

              {/* Horizontal Orange Dividers */}
              <div
                className="absolute left-0 right-0 bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500 shadow-lg"
                style={{
                  top: '33.33%',
                  height: '80px',
                  transform: 'translateY(-50%)',
                  borderRadius: '40px 0 40px 0'
                }}
              ></div>
              <div
                className="absolute left-0 right-0 bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-500 shadow-lg"
                style={{
                  top: '66.66%',
                  height: '80px',
                  transform: 'translateY(-50%)',
                  borderRadius: '40px 0 40px 0'
                }}
              ></div>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-2 grid-rows-3 h-full relative z-20">
              {services.map((service) => (
                <div
                  key={service.id}
                  className="p-10 bg-white relative flex flex-col justify-between hover:bg-gray-50 transition-all duration-300 group"
                  style={{ minHeight: '300px' }}
                >
                  <div>
                    {/* Icon */}
                    <div className="mb-8">
                      <div className="w-20 h-20 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                        <Image
                          src={service.icon}
                          alt={service.title}
                          width={40}
                          height={40}
                          className="w-10 h-10 object-contain filter brightness-75 group-hover:brightness-50 transition-all duration-300"
                        />
                      </div>
                    </div>

                    {/* Content */}
                    <h3 className="text-2xl font-bold text-gray-900 mb-5 leading-tight group-hover:text-orange-600 transition-colors duration-300">
                      {service.title}
                    </h3>

                    <p className="text-gray-600 text-sm leading-relaxed pr-6 group-hover:text-gray-700 transition-colors duration-300">
                      {service.description}
                    </p>
                  </div>

                  {/* Button */}
                  <div className="flex justify-end mt-8">
                    <button className="w-14 h-14 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-110 group-hover:rotate-12">
                      <span className="text-2xl font-bold">→</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
