import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business", 
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    },
    {
      id: 5,
      title: "Web App Development",
      description: "Build your business success with web applications that help boost all your work helps. The web apps developed by our developers will meet all your requirements and help achieve your business goals.",
      icon: "/icons/app-development.png"
    },
    {
      id: 6,
      title: "Website Revamp",
      description: "Give your website a fresh new look and attract more customers. We can help your website get a complete look with advanced new and fresh developments.",
      icon: "/icons/revamp.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "#f97316" },
    { name: "MOBILE APPS", color: "#14b8a6" },
    { name: "DIGITAL MARKETING", color: "#a855f7" },
    { name: "BRAND IDENTITY", color: "#3b82f6" }
  ];

  return (
    <section style={{
      padding: '5rem 0',
      backgroundColor: '#e5e7eb',
      minHeight: '100vh',
      position: 'relative'
    }}>
      <div style={{
        maxWidth: '100%',
        margin: '0 auto',
        padding: '0 2rem'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h2 style={{
            fontSize: '2.75rem',
            fontWeight: 'bold',
            color: '#1a1a1a',
            marginBottom: '1rem',
            fontFamily: 'system-ui, sans-serif',
            letterSpacing: '-0.025em'
          }}>
            Our Specialized Services
          </h2>
          <p style={{
            color: '#666666',
            fontSize: '1.1rem',
            marginBottom: '2.5rem',
            fontFamily: 'system-ui, sans-serif'
          }}>
            We Develop all your Digital Needs
          </p>

          {/* Category Pills */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            flexWrap: 'wrap',
            marginBottom: '4rem'
          }}>
            {categories.map((category, index) => (
              <span
                key={index}
                style={{
                  backgroundColor: category.color,
                  color: '#ffffff',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '25px',
                  fontSize: '0.8rem',
                  fontWeight: '600',
                  fontFamily: 'system-ui, sans-serif',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Main Container - Full Width */}
        <div style={{
          width: '100%',
          margin: '0',
          position: 'relative',
          padding: '0 2rem'
        }}>

          {/* Orange Background Area - Like Reference */}
          <div style={{
            position: 'absolute',
            top: '0',
            left: '2rem',
            right: '2rem',
            bottom: '0',
            backgroundColor: 'rgba(255, 140, 66, 0.1)',
            borderRadius: '20px',
            zIndex: 0
          }}></div>

          {/* Orange Cross Background */}
          <div style={{
            position: 'absolute',
            top: '0',
            left: '2rem',
            right: '2rem',
            bottom: '0',
            zIndex: 1
          }}>


            {/* Horizontal Orange Strip - Only one for 2 rows */}
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '0',
              right: '0',
              height: '60px',
              transform: 'translateY(-50%)',
              background: 'linear-gradient(45deg, #ff6b35 0%, #ff8c42 50%, #ffa726 100%)',
              borderRadius: '30px'
            }}></div>

            {/* Vertical Orange Strips - Two for 3 columns */}
            <div style={{
              position: 'absolute',
              top: '0',
              bottom: '0',
              left: '33.33%',
              width: '60px',
              transform: 'translateX(-50%)',
              background: 'linear-gradient(45deg, #ff6b35 0%, #ff8c42 50%, #ffa726 100%)',
              borderRadius: '30px'
            }}></div>

            <div style={{
              position: 'absolute',
              top: '0',
              bottom: '0',
              left: '66.66%',
              width: '60px',
              transform: 'translateX(-50%)',
              background: 'linear-gradient(45deg, #ff6b35 0%, #ff8c42 50%, #ffa726 100%)',
              borderRadius: '30px'
            }}></div>
          </div>

          {/* Services Grid - 3x2 Layout Full Width */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            gridTemplateRows: '1fr 1fr',
            gap: '2rem',
            position: 'relative',
            zIndex: 2,
            minHeight: '600px',
            width: '100%'
          }}>
            {services.map((service) => (
              <div key={service.id} style={{
                backgroundColor: '#ffffff',
                borderRadius: '16px',
                padding: '2rem',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                position: 'relative',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}>
                <div>
                  {/* Icon */}
                  <div style={{ marginBottom: '1.5rem' }}>
                    <div style={{
                      width: '50px',
                      height: '50px',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <Image
                        src={service.icon}
                        alt={service.title}
                        width={28}
                        height={28}
                        style={{ width: '28px', height: '28px', objectFit: 'contain' }}
                      />
                    </div>
                  </div>

                  {/* Title */}
                  <h3 style={{
                    fontSize: '1.1rem',
                    fontWeight: '700',
                    color: '#1a1a1a',
                    marginBottom: '1rem',
                    fontFamily: 'system-ui, sans-serif',
                    lineHeight: '1.3'
                  }}>
                    {service.title}
                  </h3>

                  {/* Description */}
                  <p style={{
                    color: '#666666',
                    fontSize: '0.8rem',
                    lineHeight: '1.5',
                    fontFamily: 'system-ui, sans-serif',
                    marginBottom: '1.5rem'
                  }}>
                    {service.description}
                  </p>
                </div>

                {/* Gradient Button */}
                <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <button style={{
                    width: '40px',
                    height: '40px',
                    background: 'linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ffa726 100%)',
                    color: '#ffffff',
                    borderRadius: '50%',
                    border: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    fontSize: '1.2rem',
                    fontWeight: 'bold',
                    boxShadow: '0 4px 12px rgba(255, 107, 53, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 6px 16px rgba(255, 107, 53, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 4px 12px rgba(255, 107, 53, 0.3)';
                  }}
                  >
                    →
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
