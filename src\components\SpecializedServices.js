import React from 'react';
import Image from 'next/image';

const SpecializedServices = () => {
  const services = [
    {
      id: 1,
      title: "Web For Start-up",
      description: "Get your new start-up with a stunning website and build your brand with our professional company in Web Development. We create websites for startups or affordable custom-built online companies support.",
      icon: "/icons/startup.png"
    },
    {
      id: 2,
      title: "Web For Growing Business", 
      description: "Having online presence plays a vital role in marketing for any business. We create a website designed to suit your marketing, development and help your customers accomplish what they are looking and an encouraging user experience.",
      icon: "/icons/webgrow.png"
    },
    {
      id: 3,
      title: "Web For Enterprise",
      description: "Our web development team understands solutions to help businesses with a custom website that reflects their personal and professional. Our effective and reliable websites are custom designed to suit your clients at the best price.",
      icon: "/icons/enterprice.png"
    },
    {
      id: 4,
      title: "E-commerce Development",
      description: "A guide for e-commerce business demands a great user experience. The success depends on e-commerce businesses go online to grow and build an effective sales and customer base of products and/or service options.",
      icon: "/icons/eccomerce.png"
    }
  ];

  const categories = [
    { name: "WEB DEVELOPMENT", color: "#f97316" },
    { name: "MOBILE APPS", color: "#14b8a6" },
    { name: "DIGITAL MARKETING", color: "#a855f7" },
    { name: "BRAND IDENTITY", color: "#3b82f6" }
  ];

  return (
    <section style={{ 
      padding: '4rem 0', 
      backgroundColor: '#f3f4f6',
      minHeight: '100vh'
    }}>
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '0 1rem' 
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h2 style={{ 
            fontSize: '2.5rem', 
            fontWeight: 'bold', 
            color: '#000000', 
            marginBottom: '0.75rem',
            fontFamily: 'system-ui, sans-serif'
          }}>
            Our Specialized Services
          </h2>
          <p style={{ 
            color: '#6b7280', 
            fontSize: '1rem', 
            marginBottom: '2rem',
            fontFamily: 'system-ui, sans-serif'
          }}>
            We Develop all your Digital Needs
          </p>
          
          {/* Category Pills */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: '0.75rem', 
            flexWrap: 'wrap', 
            marginBottom: '3rem' 
          }}>
            {categories.map((category, index) => (
              <span
                key={index}
                style={{
                  backgroundColor: category.color,
                  color: '#ffffff',
                  padding: '0.5rem 1.25rem',
                  borderRadius: '9999px',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  fontFamily: 'system-ui, sans-serif'
                }}
              >
                {category.name}
              </span>
            ))}
          </div>
        </div>

        {/* Main Container - Exact Reference Match */}
        <div style={{ 
          maxWidth: '900px', 
          margin: '0 auto',
          position: 'relative'
        }}>
          <div style={{
            backgroundColor: '#ffffff',
            borderRadius: '24px',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            overflow: 'hidden',
            position: 'relative',
            minHeight: '600px'
          }}>
            
            {/* Orange Cross - Exact Reference Design */}
            <div style={{ position: 'absolute', inset: '0', zIndex: 1 }}>
              {/* Vertical Orange Strip */}
              <div style={{
                position: 'absolute',
                top: '0',
                bottom: '0',
                left: '50%',
                width: '80px',
                transform: 'translateX(-50%)',
                background: 'linear-gradient(180deg, #ff8c42 0%, #ffa726 30%, #ffb84d 70%, #ff8c42 100%)',
                borderRadius: '0 0 40px 40px'
              }}></div>
              
              {/* Horizontal Orange Strip */}
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '0',
                right: '0',
                height: '80px',
                transform: 'translateY(-50%)',
                background: 'linear-gradient(90deg, #ff8c42 0%, #ffa726 30%, #ffb84d 70%, #ff8c42 100%)',
                borderRadius: '40px 0 40px 0'
              }}></div>
            </div>

            {/* Services Grid - 2x2 Layout like Reference */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gridTemplateRows: '1fr 1fr',
              height: '600px',
              position: 'relative',
              zIndex: 2
            }}>
              {services.map((service, index) => (
                <div key={service.id} style={{
                  padding: '2rem',
                  backgroundColor: '#ffffff',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }}>
                  <div>
                    <div style={{ marginBottom: '1.5rem' }}>
                      <div style={{
                        width: '60px',
                        height: '60px',
                        backgroundColor: '#fff7ed',
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <Image
                          src={service.icon}
                          alt={service.title}
                          width={32}
                          height={32}
                          style={{ width: '32px', height: '32px', objectFit: 'contain' }}
                        />
                      </div>
                    </div>
                    <h3 style={{
                      fontSize: '1.25rem',
                      fontWeight: 'bold',
                      color: '#000000',
                      marginBottom: '1rem',
                      fontFamily: 'system-ui, sans-serif'
                    }}>
                      {service.title}
                    </h3>
                    <p style={{
                      color: '#6b7280',
                      fontSize: '0.875rem',
                      lineHeight: '1.5',
                      fontFamily: 'system-ui, sans-serif'
                    }}>
                      {service.description}
                    </p>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
                    <button style={{
                      width: '40px',
                      height: '40px',
                      backgroundColor: '#f97316',
                      color: '#ffffff',
                      borderRadius: '50%',
                      border: 'none',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      fontSize: '1.25rem',
                      fontWeight: 'bold'
                    }}>
                      →
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SpecializedServices;
