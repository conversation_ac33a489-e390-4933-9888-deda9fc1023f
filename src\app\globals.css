@import "tailwindcss";

/* Base theme variables - will be overridden by ThemeProvider */
:root {
  /* Legacy variables for backward compatibility */
  --background: #ffffff;
  --foreground: #171717;

  /* Theme system variables - these will be set by ThemeProvider */
  /* Background colors */
  --theme-background-primary: #f9fafb;
  --theme-background-secondary: #f3f4f6;
  --theme-background-tertiary: #e5e7eb;
  --theme-background-elevated: #ffffff;
  --theme-background-overlay: rgba(0, 0, 0, 0.5);

  /* Text colors */
  --theme-text-primary: #111827;
  --theme-text-secondary: #374151;
  --theme-text-tertiary: #6b7280;
  --theme-text-inverse: #ffffff;
  --theme-text-disabled: #9ca3af;

  /* Border colors */
  --theme-border-primary: #e5e7eb;
  --theme-border-secondary: #d1d5db;
  --theme-border-focus: #3b82f6;
  --theme-border-error: #ef4444;

  /* Interactive colors */
  --theme-interactive-primary: #2563eb;
  --theme-interactive-primaryHover: #1d4ed8;
  --theme-interactive-primaryActive: #1e40af;
  --theme-interactive-secondary: #475569;
  --theme-interactive-secondaryHover: #334155;
  --theme-interactive-accent: #c026d3;
  --theme-interactive-accentHover: #a21caf;

  /* Status colors */
  --theme-status-success: #16a34a;
  --theme-status-successBg: #f0fdf4;
  --theme-status-warning: #d97706;
  --theme-status-warningBg: #fffbeb;
  --theme-status-error: #dc2626;
  --theme-status-errorBg: #fef2f2;
  --theme-status-info: #2563eb;
  --theme-status-infoBg: #eff6ff;

  /* Shadow values */
  --theme-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --theme-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --theme-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --theme-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark theme overrides for system preference (fallback) */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Dark theme defaults */
    --theme-background-primary: #030712;
    --theme-background-secondary: #111827;
    --theme-background-tertiary: #1f2937;
    --theme-background-elevated: #111827;
    --theme-background-overlay: rgba(0, 0, 0, 0.8);

    --theme-text-primary: #f9fafb;
    --theme-text-secondary: #d1d5db;
    --theme-text-tertiary: #9ca3af;
    --theme-text-inverse: #111827;
    --theme-text-disabled: #4b5563;

    --theme-border-primary: #1f2937;
    --theme-border-secondary: #374151;
    --theme-border-focus: #60a5fa;
    --theme-border-error: #f87171;

    --theme-interactive-primary: #3b82f6;
    --theme-interactive-primaryHover: #60a5fa;
    --theme-interactive-primaryActive: #93c5fd;
    --theme-interactive-secondary: #94a3b8;
    --theme-interactive-secondaryHover: #cbd5e1;
    --theme-interactive-accent: #e879f9;
    --theme-interactive-accentHover: #f0abfc;

    --theme-status-success: #4ade80;
    --theme-status-successBg: #052e16;
    --theme-status-warning: #fbbf24;
    --theme-status-warningBg: #451a03;
    --theme-status-error: #f87171;
    --theme-status-errorBg: #450a0a;
    --theme-status-info: #60a5fa;
    --theme-status-infoBg: #172554;

    --theme-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --theme-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --theme-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --theme-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  }
}

/* Tailwind theme integration */
@theme inline {
  /* Legacy support */
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Font families */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Theme-aware colors for Tailwind */
  --color-theme-bg-primary: var(--theme-background-primary);
  --color-theme-bg-secondary: var(--theme-background-secondary);
  --color-theme-bg-tertiary: var(--theme-background-tertiary);
  --color-theme-bg-elevated: var(--theme-background-elevated);

  --color-theme-text-primary: var(--theme-text-primary);
  --color-theme-text-secondary: var(--theme-text-secondary);
  --color-theme-text-tertiary: var(--theme-text-tertiary);
  --color-theme-text-inverse: var(--theme-text-inverse);

  --color-theme-border-primary: var(--theme-border-primary);
  --color-theme-border-secondary: var(--theme-border-secondary);
  --color-theme-border-focus: var(--theme-border-focus);

  --color-theme-interactive-primary: var(--theme-interactive-primary);
  --color-theme-interactive-secondary: var(--theme-interactive-secondary);
  --color-theme-interactive-accent: var(--theme-interactive-accent);

  --color-theme-success: var(--theme-status-success);
  --color-theme-warning: var(--theme-status-warning);
  --color-theme-error: var(--theme-status-error);
  --color-theme-info: var(--theme-status-info);

  /* Shadow utilities */
  --shadow-theme-sm: var(--theme-shadow-sm);
  --shadow-theme-md: var(--theme-shadow-md);
  --shadow-theme-lg: var(--theme-shadow-lg);
  --shadow-theme-xl: var(--theme-shadow-xl);
}

/* Base body styles */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

body {
  background: var(--theme-background-primary);
  color: var(--theme-text-primary);
  font-family: var(--font-sans), system-ui, sans-serif;
  transition: background-color 150ms ease-in-out, color 150ms ease-in-out;
}

/* Ensure main container takes full width */
#__next {
  width: 100%;
  min-height: 100vh;
}

/* Theme transition for all elements */
* {
  transition: border-color 150ms ease-in-out;
}

/* Utility classes for theme-aware styling */
.theme-bg-primary { background-color: var(--theme-background-primary); }
.theme-bg-secondary { background-color: var(--theme-background-secondary); }
.theme-bg-tertiary { background-color: var(--theme-background-tertiary); }
.theme-bg-elevated { background-color: var(--theme-background-elevated); }

.theme-text-primary { color: var(--theme-text-primary); }
.theme-text-secondary { color: var(--theme-text-secondary); }
.theme-text-tertiary { color: var(--theme-text-tertiary); }
.theme-text-inverse { color: var(--theme-text-inverse); }

.theme-border-primary { border-color: var(--theme-border-primary); }
.theme-border-secondary { border-color: var(--theme-border-secondary); }
.theme-border-focus { border-color: var(--theme-border-focus); }

.theme-shadow-sm { box-shadow: var(--theme-shadow-sm); }
.theme-shadow-md { box-shadow: var(--theme-shadow-md); }
.theme-shadow-lg { box-shadow: var(--theme-shadow-lg); }
.theme-shadow-xl { box-shadow: var(--theme-shadow-xl); }

/* Theme Toggle Component Styles */
.theme-toggle {
  @apply inline-flex items-center justify-center rounded-md transition-all duration-150 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  color: var(--theme-text-primary);
  border-color: var(--theme-border-primary);
  background-color: var(--theme-background-secondary);
}

.theme-toggle:hover:not(:disabled) {
  background-color: var(--theme-background-tertiary);
  transform: scale(1.05);
}

.theme-toggle:focus {
  ring-color: var(--theme-border-focus);
}

.theme-toggle-icon {
  @apply flex items-center justify-center;
}

.theme-toggle-label {
  @apply ml-2 text-sm font-medium;
}

.theme-toggle-skeleton {
  @apply inline-flex items-center justify-center rounded-md border;
  background-color: var(--theme-background-secondary);
  border-color: var(--theme-border-primary);
}

/* Theme Selector Styles */
.theme-selector-buttons {
  @apply inline-flex rounded-lg border p-1 space-x-1;
  background-color: var(--theme-background-secondary);
  border-color: var(--theme-border-primary);
}

.theme-option-button {
  @apply inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-150;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  color: var(--theme-text-secondary);
}

.theme-option-button:hover {
  color: var(--theme-text-primary);
  background-color: var(--theme-background-tertiary);
}

.theme-option-button.active {
  color: var(--theme-text-inverse);
  background-color: var(--theme-interactive-primary);
  box-shadow: var(--theme-shadow-sm);
}

.theme-option-button:focus {
  ring-color: var(--theme-border-focus);
}

.theme-option-icon {
  @apply flex items-center justify-center mr-2;
}

.theme-option-label {
  @apply whitespace-nowrap;
}

/* Dropdown Selector Styles */
.theme-selector-dropdown {
  @apply relative inline-block;
}

.theme-selector-trigger {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-md border transition-all duration-150;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  color: var(--theme-text-primary);
  background-color: var(--theme-background-elevated);
  border-color: var(--theme-border-primary);
}

.theme-selector-trigger:hover {
  background-color: var(--theme-background-secondary);
}

.theme-selector-trigger:focus {
  ring-color: var(--theme-border-focus);
}

.theme-selector-current-icon {
  @apply flex items-center justify-center mr-2;
}

.theme-selector-current-label {
  @apply mr-2;
}

.theme-selector-chevron {
  @apply transition-transform duration-150;
}

.theme-selector-chevron.open {
  @apply rotate-180;
}

.theme-selector-menu {
  @apply absolute right-0 mt-2 w-48 rounded-md border shadow-lg z-50;
  @apply py-1 focus:outline-none;
  background-color: var(--theme-background-elevated);
  border-color: var(--theme-border-primary);
  box-shadow: var(--theme-shadow-lg);
}

.theme-selector-option {
  @apply flex items-center w-full px-4 py-2 text-sm transition-colors duration-150;
  @apply focus:outline-none;
  color: var(--theme-text-primary);
}

.theme-selector-option:hover {
  background-color: var(--theme-background-secondary);
}

.theme-selector-option.active {
  color: var(--theme-interactive-primary);
  background-color: var(--theme-status-infoBg);
}

.theme-option-check {
  @apply ml-auto;
}

.theme-selector-skeleton {
  @apply inline-block;
}

/* Navbar Styles */
.top-banner {
  transition: height 0.3s ease-in-out;
}

.main-navbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-height: 80px;
}

.navbar-green-bg {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 50%, #166534 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.navbar-teal-bg {
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 50%, #134e4a 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.navbar-dark-bg {
  background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #030712 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* Logo styling */
.main-navbar .logo-section {
  padding: 0.5rem 0;
}

.main-navbar .logo-container {
  transition: transform 0.2s ease-in-out;
}

.main-navbar .logo-container:hover {
  transform: scale(1.05);
}

.nav-item-green {
  position: relative;
  transition: all 0.2s ease-in-out;
  border-radius: 0.5rem;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.nav-item-green:hover {
  transform: translateY(-1px);
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.nav-item-green::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #ffffff;
  transition: all 0.3s ease-in-out;
  transform: translateX(-50%);
  border-radius: 1px;
}

.nav-item-green:hover::after {
  width: 80%;
}

.nav-item:hover {
  transform: translateY(-2px);
  background-color: var(--theme-background-secondary);
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 3px;
  background-color: var(--theme-interactive-primary);
  transition: all 0.3s ease-in-out;
  transform: translateX(-50%);
  border-radius: 2px;
}

.nav-item:hover::after {
  width: 90%;
}

.nav-item .flex {
  align-items: center;
  justify-content: center;
}

.mobile-menu-button {
  transition: transform 0.2s ease-in-out;
}

.mobile-menu-button:hover {
  transform: scale(1.1);
}

.mobile-nav-item {
  transition: all 0.2s ease-in-out;
  border-left: 3px solid transparent;
}

.mobile-nav-item:hover {
  border-left-color: var(--theme-interactive-primary);
  padding-left: 1rem;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .nav-item {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 768px) {
  .top-banner .flex {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem 0;
  }

  .top-banner .flex > div:last-child {
    flex-direction: row;
    gap: 1rem;
  }
}

/* Floating Theme Selector Styles */
.floating-theme-selector {
  position: fixed !important;
  bottom: 30px !important;
  right: 30px !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
  width: 60px !important;
  height: 60px !important;
}

.floating-main-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  position: relative;
  z-index: 10000;
  background-color: #3b82f6 !important;
}

.floating-main-button:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.floating-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-icon.open {
  transform: rotate(180deg);
}

.floating-option {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: floatingOptionIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform-origin: center;
  z-index: 9999;
}

.floating-option:hover {
  transform: scale(1.15) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

@keyframes floatingOptionIn {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0);
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation for floating theme options */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0);
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.floating-theme-option {
  animation: fadeInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Responsive adjustments for floating selector */
@media (max-width: 768px) {
  .floating-theme-selector {
    bottom: 20px;
    right: 20px;
  }

  .floating-main-button {
    width: 55px;
    height: 55px;
  }

  .floating-option {
    width: 45px;
    height: 45px;
  }
}
