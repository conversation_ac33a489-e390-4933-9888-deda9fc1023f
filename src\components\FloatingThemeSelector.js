'use client';

import React, { useState } from 'react';

const FloatingThemeSelector = () => {
  const [isOpen, setIsOpen] = useState(false);

  const themes = [
    { name: 'Light', icon: '☀️', color: '#f59e0b', value: 'light' },
    { name: 'Dark', icon: '🌙', color: '#374151', value: 'dark' },
    { name: 'Teal', icon: '🎨', color: '#0d9488', value: 'teal' }
  ];

  const handleThemeChange = (themeValue) => {
    console.log('Setting theme to:', themeValue);

    const root = document.documentElement;

    // Set theme attribute
    root.setAttribute('data-theme', themeValue);

    // Apply ALL CSS variables for complete theme change
    if (themeValue === 'dark') {
      // Dark theme variables
      root.style.setProperty('--theme-background-primary', '#0f172a');
      root.style.setProperty('--theme-background-secondary', '#111827');
      root.style.setProperty('--theme-background-tertiary', '#1f2937');
      root.style.setProperty('--theme-background-elevated', '#111827');
      root.style.setProperty('--theme-text-primary', '#f9fafb');
      root.style.setProperty('--theme-text-secondary', '#d1d5db');
      root.style.setProperty('--theme-text-tertiary', '#9ca3af');
      root.style.setProperty('--theme-text-inverse', '#111827');
      root.style.setProperty('--theme-border-primary', '#374151');
      root.style.setProperty('--theme-interactive-primary', '#3b82f6');
    } else if (themeValue === 'teal') {
      // Teal theme variables
      root.style.setProperty('--theme-background-primary', '#f0fdfa');
      root.style.setProperty('--theme-background-secondary', '#ccfbf1');
      root.style.setProperty('--theme-background-tertiary', '#99f6e4');
      root.style.setProperty('--theme-background-elevated', '#ffffff');
      root.style.setProperty('--theme-text-primary', '#134e4a');
      root.style.setProperty('--theme-text-secondary', '#0f766e');
      root.style.setProperty('--theme-text-tertiary', '#14b8a6');
      root.style.setProperty('--theme-text-inverse', '#ffffff');
      root.style.setProperty('--theme-border-primary', '#5eead4');
      root.style.setProperty('--theme-interactive-primary', '#0d9488');
    } else {
      // Light theme variables
      root.style.setProperty('--theme-background-primary', '#f9fafb');
      root.style.setProperty('--theme-background-secondary', '#f3f4f6');
      root.style.setProperty('--theme-background-tertiary', '#e5e7eb');
      root.style.setProperty('--theme-background-elevated', '#ffffff');
      root.style.setProperty('--theme-text-primary', '#111827');
      root.style.setProperty('--theme-text-secondary', '#374151');
      root.style.setProperty('--theme-text-tertiary', '#6b7280');
      root.style.setProperty('--theme-text-inverse', '#ffffff');
      root.style.setProperty('--theme-border-primary', '#d1d5db');
      root.style.setProperty('--theme-interactive-primary', '#3b82f6');
    }

    // Save to localStorage
    localStorage.setItem('brtsoft-theme', themeValue);

    // Force immediate re-render by triggering a custom event
    window.dispatchEvent(new Event('themechange'));

    setIsOpen(false);
  };

  return (
    <div style={{
      position: 'fixed',
      bottom: '30px',
      right: '20px',
      zIndex: 999999,  // Much higher z-index
      pointerEvents: 'auto'
    }}>
      {/* Backdrop */}
      {isOpen && (
        <div
          onClick={() => setIsOpen(false)}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            zIndex: 99998
          }}
        />
      )}

      {/* Theme Options - Simple horizontal layout */}
      {isOpen && (
        <div style={{
          position: 'absolute',
          bottom: '70px',
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          flexDirection: 'column',
          gap: '10px',
          alignItems: 'center',
          zIndex: 999999,
          pointerEvents: 'auto'
        }}>
          {themes.map((theme, index) => (
            <button
              key={theme.name}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Button clicked:', theme.name);
                handleThemeChange(theme.value);
              }}
              style={{
                width: '45px',
                height: '45px',
                borderRadius: '50%',
                backgroundColor: theme.color,
                border: '2px solid white',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                transition: 'all 0.2s ease',
                fontSize: '18px',
                zIndex: 999999,
                pointerEvents: 'auto',
                position: 'relative' // Ensure proper stacking
              }}
              title={theme.name}
              onMouseEnter={(e) => {
                e.target.style.transform = 'scale(1.2)';
                e.target.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'scale(1)';
                e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
              }}
            >
              {theme.icon}
            </button>
          ))}
        </div>
      )}

      {/* Main Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        style={{
          width: '60px',
          height: '60px',
          borderRadius: '50%',
          backgroundColor: '#3b82f6',
          border: 'none',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.3)',
          transition: 'all 0.3s ease',
          transform: isOpen ? 'rotate(180deg) scale(1.1)' : 'scale(1)',
          zIndex: 999999,
          position: 'relative',
          pointerEvents: 'auto'
        }}
        title="Theme Selector"
        onMouseEnter={(e) => {
          if (!isOpen) e.target.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          if (!isOpen) e.target.style.transform = 'scale(1)';
        }}
      >
        <span style={{ fontSize: '24px', color: 'white' }}>🎨</span>
      </button>


    </div>
  );
};

export default FloatingThemeSelector;
