'use client';

import Navbar from '../components/Navbar';
import FloatingThemeSelector from '../components/FloatingThemeSelector';

export default function Home() {
  return (
    <div className="min-h-screen theme-bg-primary">
      <Navbar />

      {/* Main Content */}
      <main className="pt-8">
        {/* Hero Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold theme-text-primary mb-6">
              Welcome to BRTSoft
            </h1>
            <p className="text-xl theme-text-secondary mb-8 max-w-2xl mx-auto">
              Your trusted technology partner for innovative solutions and digital transformation.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105"
                style={{
                  backgroundColor: 'var(--theme-interactive-primary)',
                  color: 'var(--theme-text-inverse)'
                }}
              >
                Get Started
              </button>
              <button className="px-8 py-3 rounded-lg font-medium border transition-all duration-200 theme-text-primary theme-border-primary hover:theme-bg-secondary">
                Learn More
              </button>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section id="services" className="py-16 px-4 sm:px-6 lg:px-8 theme-bg-secondary">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold theme-text-primary text-center mb-12">
              Our Services
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Web Development', icon: '🌐', desc: 'Modern web applications' },
                { title: 'Mobile Apps', icon: '📱', desc: 'iOS and Android development' },
                { title: 'Cloud Solutions', icon: '☁️', desc: 'Scalable cloud infrastructure' },
                { title: 'Consulting', icon: '💡', desc: 'Technology consulting services' }
              ].map((service, index) => (
                <div key={index} className="theme-bg-elevated p-6 rounded-lg theme-shadow-md hover:theme-shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                  <div className="text-3xl mb-4">{service.icon}</div>
                  <h3 className="text-lg font-semibold theme-text-primary mb-2">{service.title}</h3>
                  <p className="theme-text-secondary text-sm">{service.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold theme-text-primary mb-8">
              Get In Touch
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="theme-bg-elevated p-6 rounded-lg theme-shadow-sm">
                <h3 className="text-lg font-semibold theme-text-primary mb-4">Contact Information</h3>
                <div className="space-y-3 text-left">
                  <div className="flex items-center space-x-3">
                    <span>📧</span>
                    <span className="theme-text-secondary">+91 9876 600 666</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span>📱</span>
                    <span className="theme-text-secondary">+971 55 899 4399</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span>📍</span>
                    <span className="theme-text-secondary">Your Business Address</span>
                  </div>
                </div>
              </div>

              <div className="theme-bg-elevated p-6 rounded-lg theme-shadow-sm">
                <h3 className="text-lg font-semibold theme-text-primary mb-4">Quick Contact</h3>
                <form className="space-y-4">
                  <input
                    type="text"
                    placeholder="Your Name"
                    className="w-full px-4 py-2 rounded-md border theme-border-primary theme-bg-primary theme-text-primary focus:theme-border-focus focus:outline-none"
                  />
                  <input
                    type="email"
                    placeholder="Your Email"
                    className="w-full px-4 py-2 rounded-md border theme-border-primary theme-bg-primary theme-text-primary focus:theme-border-focus focus:outline-none"
                  />
                  <textarea
                    placeholder="Your Message"
                    rows="3"
                    className="w-full px-4 py-2 rounded-md border theme-border-primary theme-bg-primary theme-text-primary focus:theme-border-focus focus:outline-none"
                  ></textarea>
                  <button
                    type="submit"
                    className="w-full px-4 py-2 rounded-md font-medium transition-colors"
                    style={{
                      backgroundColor: 'var(--theme-interactive-primary)',
                      color: 'var(--theme-text-inverse)'
                    }}
                  >
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="theme-bg-elevated border-t theme-border-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="theme-text-secondary text-sm">
              © 2024 BRTSoft. All rights reserved.
            </p>
            <p className="theme-text-tertiary text-xs mt-2">
              ISO 9001:2015 Certified Company
            </p>
          </div>
        </div>
      </footer>

      {/* Floating Theme Selector */}
      <FloatingThemeSelector />
    </div>
  );
}
