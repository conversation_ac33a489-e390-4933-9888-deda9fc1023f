/**
 * Theme Configuration
 * Centralized theme tokens and settings for the application
 */

export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  TEAL: 'teal',
  SYSTEM: 'system'
};

export const STORAGE_KEY = 'brtsoft-theme';

// Color palette with semantic naming
export const colors = {
  // Primary brand colors
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554'
  },
  
  // Secondary colors
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617'
  },
  
  // Accent colors
  accent: {
    50: '#fdf4ff',
    100: '#fae8ff',
    200: '#f5d0fe',
    300: '#f0abfc',
    400: '#e879f9',
    500: '#d946ef',
    600: '#c026d3',
    700: '#a21caf',
    800: '#86198f',
    900: '#701a75',
    950: '#4a044e'
  },
  
  // Success colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16'
  },
  
  // Warning colors
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03'
  },
  
  // Error colors
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a'
  },
  
  // Neutral grays
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712'
  }
};

// Semantic color tokens for light theme
export const lightTheme = {
  // Background colors
  background: {
    primary: colors.gray[50],
    secondary: colors.gray[100],
    tertiary: colors.gray[200],
    elevated: '#ffffff',
    overlay: 'rgba(0, 0, 0, 0.5)'
  },
  
  // Text colors
  text: {
    primary: colors.gray[900],
    secondary: colors.gray[700],
    tertiary: colors.gray[500],
    inverse: '#ffffff',
    disabled: colors.gray[400]
  },
  
  // Border colors
  border: {
    primary: colors.gray[200],
    secondary: colors.gray[300],
    focus: colors.primary[500],
    error: colors.error[500]
  },
  
  // Interactive colors
  interactive: {
    primary: colors.primary[600],
    primaryHover: colors.primary[700],
    primaryActive: colors.primary[800],
    secondary: colors.secondary[600],
    secondaryHover: colors.secondary[700],
    accent: colors.accent[600],
    accentHover: colors.accent[700]
  },
  
  // Status colors
  status: {
    success: colors.success[600],
    successBg: colors.success[50],
    warning: colors.warning[600],
    warningBg: colors.warning[50],
    error: colors.error[600],
    errorBg: colors.error[50],
    info: colors.primary[600],
    infoBg: colors.primary[50]
  },
  
  // Shadow colors
  shadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
  }
};

// Semantic color tokens for teal theme
export const tealTheme = {
  // Background colors
  background: {
    primary: '#f0fdfa',
    secondary: '#ccfbf1',
    tertiary: '#99f6e4',
    elevated: '#ffffff',
    overlay: 'rgba(0, 0, 0, 0.5)'
  },

  // Text colors
  text: {
    primary: '#134e4a',
    secondary: '#0f766e',
    tertiary: '#14b8a6',
    inverse: '#ffffff',
    disabled: '#5eead4'
  },

  // Border colors
  border: {
    primary: '#99f6e4',
    secondary: '#5eead4',
    focus: '#0d9488',
    error: colors.error[500]
  },

  // Interactive colors
  interactive: {
    primary: '#0d9488',
    primaryHover: '#0f766e',
    primaryActive: '#134e4a',
    secondary: '#14b8a6',
    secondaryHover: '#0d9488',
    accent: '#06b6d4',
    accentHover: '#0891b2'
  },

  // Status colors
  status: {
    success: colors.success[600],
    successBg: colors.success[50],
    warning: colors.warning[600],
    warningBg: colors.warning[50],
    error: colors.error[600],
    errorBg: colors.error[50],
    info: '#0d9488',
    infoBg: '#f0fdfa'
  },

  // Shadow colors
  shadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
  }
};

// Semantic color tokens for dark theme
export const darkTheme = {
  // Background colors
  background: {
    primary: colors.gray[950],
    secondary: colors.gray[900],
    tertiary: colors.gray[800],
    elevated: colors.gray[900],
    overlay: 'rgba(0, 0, 0, 0.8)'
  },
  
  // Text colors
  text: {
    primary: colors.gray[50],
    secondary: colors.gray[300],
    tertiary: colors.gray[400],
    inverse: colors.gray[900],
    disabled: colors.gray[600]
  },
  
  // Border colors
  border: {
    primary: colors.gray[800],
    secondary: colors.gray[700],
    focus: colors.primary[400],
    error: colors.error[400]
  },
  
  // Interactive colors
  interactive: {
    primary: colors.primary[500],
    primaryHover: colors.primary[400],
    primaryActive: colors.primary[300],
    secondary: colors.secondary[400],
    secondaryHover: colors.secondary[300],
    accent: colors.accent[400],
    accentHover: colors.accent[300]
  },
  
  // Status colors
  status: {
    success: colors.success[400],
    successBg: colors.success[950],
    warning: colors.warning[400],
    warningBg: colors.warning[950],
    error: colors.error[400],
    errorBg: colors.error[950],
    info: colors.primary[400],
    infoBg: colors.primary[950]
  },
  
  // Shadow colors
  shadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4)'
  }
};

// Typography scale
export const typography = {
  fontFamily: {
    sans: ['var(--font-geist-sans)', 'system-ui', 'sans-serif'],
    mono: ['var(--font-geist-mono)', 'monospace']
  },
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],
    sm: ['0.875rem', { lineHeight: '1.25rem' }],
    base: ['1rem', { lineHeight: '1.5rem' }],
    lg: ['1.125rem', { lineHeight: '1.75rem' }],
    xl: ['1.25rem', { lineHeight: '1.75rem' }],
    '2xl': ['1.5rem', { lineHeight: '2rem' }],
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    '5xl': ['3rem', { lineHeight: '1' }],
    '6xl': ['3.75rem', { lineHeight: '1' }]
  },
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900'
  }
};

// Spacing scale
export const spacing = {
  0: '0px',
  1: '0.25rem',
  2: '0.5rem',
  3: '0.75rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  8: '2rem',
  10: '2.5rem',
  12: '3rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  32: '8rem',
  40: '10rem',
  48: '12rem',
  56: '14rem',
  64: '16rem'
};

// Border radius scale
export const borderRadius = {
  none: '0px',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px'
};

// Animation and transition settings
export const animation = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out'
  }
};

export default {
  THEME_MODES,
  STORAGE_KEY,
  colors,
  lightTheme,
  darkTheme,
  typography,
  spacing,
  borderRadius,
  animation
};
